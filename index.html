<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-T-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Autonomous AI IDE</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      darkMode: 'class', // or 'media'
      theme: {
        extend: {
          colors: {
            'primary': {
              DEFAULT: '#3b82f6', // blue-500
              hover: '#2563eb', // blue-600
              dark: '#1e3a8a' // blue-800 custom darker for text
            },
            'secondary': '#10b981', // emerald-500
            'background': '#111827', // gray-900
            'surface': '#1f2937', // gray-800
            'on-surface': '#f3f4f6', // gray-100
            'on-surface-muted': '#9ca3af', // gray-400
            'error': '#ef4444', // red-500
          },
          fontFamily: {
            sans: ['Inter', 'sans-serif'],
            mono: ['JetBrains Mono', 'monospace'],
          },
          fontSize: {
            'xxs': '0.65rem', // For very small text like logs
          }
        }
      }
    }
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;700&display=swap" rel="stylesheet">

  <!-- Prism.js CSS (Okaidia Theme as an example) -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-okaidia.min.css" rel="stylesheet" />
  <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.css" rel="stylesheet" />



<style>
  body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }
  #root {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  /* Ensure pre/code take up space and scroll for Prism.js */
  pre[class*="language-"] {
    margin: 0; /* Reset default margins */
    border-radius: 0; /* Match editor pane style */
    font-size: 0.875rem; /* 14px, Tailwind 'text-sm' */
    line-height: 1.25rem; /* Tailwind 'leading-tight' */
  }
  .line-numbers .line-numbers-rows {
    border-right-color: #374151 !important; /* gray-700 */
    background: #1f2937; /* surface */
  }
  .line-numbers-rows > span:before {
    color: #6b7280 !important; /* gray-500 */
  }

</style>
</head>
<body class="bg-background text-on-surface font-sans antialiased">
  <div id="root"></div>
  <!-- Prism.js Core -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
  <!-- Prism.js Autoloader for languages -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  <!-- Prism.js Line Numbers plugin -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.js"></script>

  <script type="module" src="/index.tsx"></script>
</body>
</html>
