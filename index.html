<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-T-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Autonomous AI IDE</title>

  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;700&display=swap" rel="stylesheet">

  <!-- Prism.js CSS (Okaidia Theme as an example) -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-okaidia.min.css" rel="stylesheet" />
  <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.css" rel="stylesheet" />



<style>
  /* Basic reset and layout */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Inter', system-ui, sans-serif;
    background-color: #0f0f23;
    color: #e5e7eb;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  #root {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  /* Basic utility classes */
  .flex { display: flex; }
  .flex-col { flex-direction: column; }
  .flex-grow { flex-grow: 1; }
  .h-screen { height: 100vh; }
  .max-h-screen { max-height: 100vh; }
  .overflow-hidden { overflow: hidden; }
  .w-full { width: 100%; }
  .p-3 { padding: 0.75rem; }
  .p-4 { padding: 1rem; }
  .p-6 { padding: 1.5rem; }
  .px-4 { padding-left: 1rem; padding-right: 1rem; }
  .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
  .mb-2 { margin-bottom: 0.5rem; }
  .mb-4 { margin-bottom: 1rem; }
  .mt-4 { margin-top: 1rem; }
  .space-x-3 > * + * { margin-left: 0.75rem; }
  .items-center { align-items: center; }
  .justify-between { justify-content: space-between; }
  .justify-center { justify-content: center; }
  .text-center { text-align: center; }
  .text-xl { font-size: 1.25rem; }
  .text-lg { font-size: 1.125rem; }
  .text-sm { font-size: 0.875rem; }
  .text-xs { font-size: 0.75rem; }
  .font-semibold { font-weight: 600; }
  .rounded { border-radius: 0.25rem; }
  .rounded-lg { border-radius: 0.5rem; }
  .border { border-width: 1px; }
  .border-b { border-bottom-width: 1px; }
  .border-t { border-top-width: 1px; }
  .shrink-0 { flex-shrink: 0; }

  /* Colors */
  .bg-background { background-color: #0f0f23; }
  .bg-surface { background-color: #1a1a2e; }
  .bg-gray-800 { background-color: #1f2937; }
  .bg-gray-700 { background-color: #374151; }
  .bg-blue-500 { background-color: #3b82f6; }
  .bg-blue-600 { background-color: #2563eb; }
  .bg-error { background-color: #ef4444; }
  .text-on-surface { color: #e5e7eb; }
  .text-on-surface-muted { color: #9ca3af; }
  .text-gray-100 { color: #f3f4f6; }
  .text-gray-300 { color: #d1d5db; }
  .text-gray-400 { color: #9ca3af; }
  .text-white { color: #ffffff; }
  .text-error { color: #ef4444; }
  .border-gray-700 { border-color: #374151; }

  /* Interactive */
  .hover\:bg-blue-600:hover { background-color: #2563eb; }
  .hover\:bg-gray-600:hover { background-color: #4b5563; }
  .hover\:border-gray-600:hover { border-color: #4b5563; }
  .cursor-pointer { cursor: pointer; }
  .transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out; }

  /* Additional utility classes */
  .grid { display: grid; }
  .gap-4 { gap: 1rem; }
  .gap-6 { gap: 1.5rem; }
  .max-w-md { max-width: 28rem; }
  .max-w-2xl { max-width: 42rem; }
  .min-h-screen { min-height: 100vh; }
  .h-8 { height: 2rem; }
  .w-8 { width: 2rem; }
  .h-6 { height: 1.5rem; }
  .w-6 { width: 1.5rem; }
  .fixed { position: fixed; }
  .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
  .z-50 { z-index: 50; }
  .relative { position: relative; }
  .absolute { position: absolute; }
  .top-0 { top: 0; }
  .right-0 { right: 0; }
  .bottom-0 { bottom: 0; }
  .left-0 { left: 0; }
  .opacity-50 { opacity: 0.5; }
  .opacity-75 { opacity: 0.75; }
  .opacity-90 { opacity: 0.9; }
  .bg-black { background-color: #000000; }
  .bg-white { background-color: #ffffff; }
  .text-black { color: #000000; }
  .select-none { user-select: none; }
  .pointer-events-none { pointer-events: none; }
  .overflow-auto { overflow: auto; }
  .overflow-y-auto { overflow-y: auto; }
  .overflow-x-hidden { overflow-x: hidden; }
  .whitespace-pre-wrap { white-space: pre-wrap; }
  .break-words { word-wrap: break-word; }
  .font-mono { font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace; }
  .leading-tight { line-height: 1.25; }
  .leading-relaxed { line-height: 1.625; }
  .tracking-wide { letter-spacing: 0.025em; }
  .uppercase { text-transform: uppercase; }
  .capitalize { text-transform: capitalize; }
  .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
  .shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
  .focus\:outline-none:focus { outline: none; }
  .focus\:ring-2:focus { box-shadow: 0 0 0 2px #4f46e5; }
  .focus\:border-transparent:focus { border-color: transparent; }
  .disabled\:opacity-50:disabled { opacity: 0.5; }
  .disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }

  /* Prism.js styles */
  pre[class*="language-"] {
    margin: 0;
    border-radius: 0;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
  .line-numbers .line-numbers-rows {
    border-right-color: #374151 !important;
    background: #1f2937;
  }
  .line-numbers-rows > span:before {
    color: #6b7280 !important;
  }
</style>
</head>
<body class="bg-background text-on-surface font-sans antialiased">
  <div id="root"></div>
  <!-- Prism.js Core -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
  <!-- Prism.js Autoloader for languages -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  <!-- Prism.js Line Numbers plugin -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.js"></script>

  <script type="module" src="/index.tsx"></script>
</body>
</html>
