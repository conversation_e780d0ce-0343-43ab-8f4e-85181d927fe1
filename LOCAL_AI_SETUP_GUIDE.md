# 🏠 本地 AI 模型完整设置指南

## 🎯 为什么选择本地 AI？

✅ **完全免费** - 无需 API 密钥，无使用限制  
✅ **隐私保护** - 代码和数据不离开您的设备  
✅ **离线工作** - 无需网络连接  
✅ **无限使用** - 没有请求次数限制  
✅ **自定义控制** - 完全控制模型参数  

## 🚀 支持的本地 AI 解决方案

### 1. 🦙 Ollama (推荐新手)

**最简单的本地 AI 解决方案**

```bash
# 安装 Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 下载推荐模型
ollama pull llama3.2:3b          # 通用编程 (3GB)
ollama pull codellama:7b         # 专业代码生成 (4GB)
ollama pull deepseek-coder:6.7b  # 优秀的代码模型 (4GB)
ollama pull qwen2.5:3b           # 中文友好 (2GB)

# 启动服务 (通常自动启动)
ollama serve

# 测试
ollama run llama3.2:3b "写一个 Python 函数计算斐波那契数列"
```

**端口**: `http://localhost:11434`  
**优势**: 一键安装，自动管理模型，简单易用  

### 2. 🖥️ LM Studio (推荐 GUI 用户)

**用户友好的图形界面**

1. 下载: https://lmstudio.ai/
2. 安装并启动 LM Studio
3. 在 "Discover" 页面搜索并下载模型:
   - `microsoft/Phi-3-mini-4k-instruct-gguf`
   - `meta-llama/Llama-3.2-3B-Instruct-gguf`
   - `deepseek-ai/deepseek-coder-6.7b-instruct-gguf`
4. 在 "Chat" 页面加载模型
5. 在 "Local Server" 页面启动服务器

**端口**: `http://localhost:1234`  
**优势**: 图形界面，模型管理简单，性能优化好  

### 3. 🔧 Text Generation WebUI (高级用户)

**功能最丰富的本地 AI 解决方案**

```bash
# 克隆仓库
git clone https://github.com/oobabooga/text-generation-webui
cd text-generation-webui

# 安装 (选择适合您系统的脚本)
# Windows: start_windows.bat
# macOS: start_macos.sh
# Linux: start_linux.sh
./start_linux.sh

# 或使用 pip 安装
pip install -r requirements.txt
python server.py --api --listen
```

**端口**: `http://localhost:5000`  
**优势**: 功能丰富，支持多种模型格式，高度可定制  

### 4. ⚡ KoboldCpp (轻量级)

**轻量级高性能解决方案**

```bash
# 下载预编译版本
# Windows: https://github.com/LostRuins/koboldcpp/releases
# 或编译源码
git clone https://github.com/LostRuins/koboldcpp
cd koboldcpp
make

# 运行 (需要下载 GGUF 模型文件)
./koboldcpp --model path/to/model.gguf --port 5001
```

**端口**: `http://localhost:5001`  
**优势**: 轻量级，高性能，支持多种硬件加速  

### 5. 🔥 llama.cpp Server (极致性能)

**C++ 实现的高性能解决方案**

```bash
# 克隆并编译
git clone https://github.com/ggerganov/llama.cpp
cd llama.cpp
make

# 下载模型 (GGUF 格式)
# 从 Hugging Face 下载，例如:
# https://huggingface.co/microsoft/Phi-3-mini-4k-instruct-gguf

# 启动服务器
./server -m path/to/model.gguf --port 8080 --host 0.0.0.0
```

**端口**: `http://localhost:8080`  
**优势**: 极致性能，内存效率高，支持多种硬件  

## 🎯 推荐模型选择

### 💻 编程专用模型
- **DeepSeek Coder 6.7B** - 优秀的代码生成和理解
- **Code Llama 7B** - Meta 的专业代码模型
- **Phi-3 Mini** - Microsoft 的高效小模型

### 🌍 通用模型
- **Llama 3.2 3B** - 平衡性能和资源消耗
- **Qwen 2.5 3B** - 中文支持优秀
- **Gemma 2 2B** - Google 的轻量级模型

### 📊 模型大小建议
- **8GB RAM**: 选择 3B 参数模型
- **16GB RAM**: 可运行 7B 参数模型
- **32GB+ RAM**: 可运行 13B+ 参数模型

## ⚙️ 自定义配置

### 环境变量配置

```bash
# 自定义端口和模型
export CUSTOM_BASE_URL="http://localhost:8000"
export CUSTOM_MODEL_ID="my-custom-model"
export CUSTOM_API_KEY="optional-key-for-auth"
```

### 多个本地服务同时运行

```bash
# Ollama (默认端口 11434)
ollama serve

# LM Studio (端口 1234)
# 在 LM Studio GUI 中启动

# Text Gen WebUI (端口 5000)
cd text-generation-webui
python server.py --api --listen --port 5000

# 自定义服务 (端口 8000)
./your-custom-server --port 8000
```

## 🔧 故障排除

### 常见问题

**Q: 模型加载失败**
```bash
# 检查模型文件是否存在
ls -la /path/to/model/

# 检查内存使用
free -h

# 重启服务
pkill -f ollama && ollama serve
```

**Q: 端口冲突**
```bash
# 检查端口占用
lsof -i :11434
netstat -tulpn | grep 11434

# 使用不同端口
ollama serve --port 11435
```

**Q: 性能优化**
```bash
# 启用 GPU 加速 (NVIDIA)
export CUDA_VISIBLE_DEVICES=0

# 调整线程数
export OMP_NUM_THREADS=8

# 使用量化模型减少内存使用
ollama pull llama3.2:3b-q4_0  # 4-bit 量化
```

## 🎯 最佳实践

### 1. 开发环境推荐配置
```bash
# 主力模型 - 代码生成
ollama pull deepseek-coder:6.7b

# 备用模型 - 快速响应
ollama pull llama3.2:3b

# 启动 IDE
npm run tauri:dev
```

### 2. 性能优化建议
- 使用 SSD 存储模型文件
- 确保足够的 RAM
- 启用硬件加速 (GPU/Metal)
- 选择合适大小的模型

### 3. 模型管理
```bash
# 查看已安装模型
ollama list

# 删除不需要的模型
ollama rm old-model:tag

# 更新模型
ollama pull model:latest
```

## 🚀 快速开始

**最简单的设置 (5 分钟)**:
```bash
# 1. 安装 Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 2. 下载推荐模型
ollama pull llama3.2:3b

# 3. 启动 IDE
npm run tauri:dev

# 4. 开始编程！
```

现在您拥有了完全免费、私密、无限制的本地 AI 编程助手！🎉
