/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Custom color scheme for the AI IDE
        'background': '#0f0f23',
        'surface': '#1a1a2e',
        'primary': '#4f46e5',
        'secondary': '#06b6d4',
        'accent': '#f59e0b',
        'on-surface': '#e5e7eb',
        'on-surface-muted': '#9ca3af',
        'error': '#ef4444',
        'success': '#10b981',
        'warning': '#f59e0b',
      },
      fontFamily: {
        'mono': ['JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', 'monospace'],
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-slow': 'bounce 2s infinite',
      }
    },
  },
  plugins: [],
}
