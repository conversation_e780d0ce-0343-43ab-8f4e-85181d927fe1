import { invoke } from '@tauri-apps/api/core';
import {
  AIProject,
  CreateProjectRequest,
  UpdateFileRequest,
  StartRefinementRequest,
  ReviewConfirmationRequest,
  AIModel,
  AIProvider,
  AIProviderConfig
} from '../types';

export const tauriService = {
  // Project management
  async getAllProjects(): Promise<AIProject[]> {
    return await invoke('get_all_projects');
  },

  async getProject(projectId: string): Promise<AIProject | null> {
    return await invoke('get_project', { projectId });
  },

  async createProject(name: string, goal: string): Promise<AIProject> {
    const request: CreateProjectRequest = { name, goal };
    return await invoke('create_project', { request });
  },

  // AI Development
  async startAIDevelopment(projectId: string): Promise<AIProject> {
    return await invoke('start_ai_development', { projectId });
  },

  // File management
  async updateFileContent(projectId: string, filePath: string, content: string): Promise<AIProject> {
    const request: UpdateFileRequest = {
      project_id: projectId,
      file_path: filePath,
      content,
    };
    return await invoke('update_file_content', { request });
  },

  // Refinement
  async startRefinement(projectId: string, prompt: string): Promise<AIProject> {
    const request: StartRefinementRequest = {
      project_id: projectId,
      prompt,
    };
    return await invoke('start_refinement', { request });
  },

  // Review
  async confirmReview(projectId: string, confirm: boolean, feedback?: string): Promise<AIProject> {
    const request: ReviewConfirmationRequest = {
      project_id: projectId,
      confirm,
      feedback,
    };
    return await invoke('confirm_review', { request });
  },

  // AI Provider Management
  async getAvailableModels(provider?: AIProvider): Promise<AIModel[]> {
    return await invoke('get_available_models', { provider });
  },

  async getEnabledProviders(): Promise<AIProviderConfig[]> {
    return await invoke('get_enabled_providers');
  },
};

// Legacy functions for backward compatibility
export const planProjectTasks = async (projectGoal: string): Promise<string[]> => {
  // This is now handled internally by the Rust backend during project creation
  throw new Error('planProjectTasks is deprecated. Use tauriService.createProject instead.');
};

export const executeDevelopmentTask = async (
  taskDescription: string,
  projectGoal: string,
  existingFiles: any[]
): Promise<string> => {
  // This is now handled internally by the Rust backend during AI development
  throw new Error('executeDevelopmentTask is deprecated. Use tauriService.startAIDevelopment instead.');
};

export const executeRefinementTask = async (
  refinementPrompt: string,
  projectGoal: string,
  existingFiles: any[],
  chatHistory?: any[]
): Promise<string> => {
  // This is now handled internally by the Rust backend during refinement
  throw new Error('executeRefinementTask is deprecated. Use tauriService.startRefinement instead.');
};
