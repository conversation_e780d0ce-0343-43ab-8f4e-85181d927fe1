import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { AppFile, ConversationTurn } from "../types";

const API_KEY = process.env.API_KEY;
let ai: GoogleGenAI | null = null;

if (API_KEY) {
  ai = new GoogleGenAI({ apiKey: API_KEY });
} else {
  console.warn("Gemini API Key (process.env.API_KEY) is not set. The application will not be able to connect to the Gemini API.");
}

const MODEL_NAME = "gemini-2.5-flash-preview-04-17";

const handleError = (error: unknown, context: string): Error => {
  console.error(`Error calling Gemini API (${context}):`, error);
  if (error instanceof Error) {
    if (error.message.includes("API key not valid")) {
      return new Error("Invalid API Key. Please check your Gemini API key.");
    }
    return new Error(`Gemini API request failed (${context}): ${error.message}`);
  }
  return new Error(`An unknown error occurred while communicating with the Gemini API (${context}).`);
};

const parseJsonResponse = (responseText: string, context: string): any => {
  let jsonStr = responseText.trim();
  const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
  const match = jsonStr.match(fenceRegex);
  if (match && match[2]) {
    jsonStr = match[2].trim();
  }

  try {
    return JSON.parse(jsonStr);
  } catch (e) {
    console.error(`Failed to parse JSON response from ${context}:`, e, "Raw text:", responseText);
    throw new Error(`AI response for ${context} was not valid JSON and could not be parsed.`);
  }
};


export const planProjectTasks = async (projectGoal: string): Promise<string[]> => {
  if (!ai) throw new Error("Gemini API client not initialized.");

  const systemInstruction = `You are an expert AI project planner specializing in Python, Flask, and SQLite backends.
Your task is to break down a high-level project goal into a sequence of specific, actionable development tasks, assuming the project will use Python, Flask for the web framework, and SQLite for the database.
Tasks should include setting up the Flask app (e.g., app.py, requirements.txt), defining models (e.g., for SQLAlchemy or direct SQLite in models.py), creating routes/views (e.g., in routes.py or directly in app.py), handling database migrations/initialization, and implementing business logic.
Each task should be a clear, concise instruction for a distinct coding or file creation/modification step.
Return this as a JSON array of strings. Do NOT include any other text, just the JSON array.
Example (for a Python/Flask/SQLite goal):
Input Goal: "Create a simple blog API with Python, Flask, and SQLite to manage posts."
Output (JSON array of strings):
["Initialize a Flask application structure (e.g., app.py, models.py, requirements.txt).", "Define a Post model in models.py for SQLite, including fields like id, title, content, author, and timestamp (consider using Flask-SQLAlchemy or direct sqlite3).", "Create a Flask blueprint or routes for post-related actions (e.g., in routes/posts.py or app.py).", "Implement a route and logic to create a new post (e.g., POST /api/posts), storing it in the SQLite database.", "Implement a route and logic to retrieve all posts (e.g., GET /api/posts) from SQLite.", "Implement a route and logic to retrieve a single post by its ID (e.g., GET /api/posts/<post_id>) from SQLite.", "Implement a route and logic to update an existing post by ID (e.g., PUT /api/posts/<post_id>) in SQLite.", "Implement a route and logic to delete a post by ID (e.g., DELETE /api/posts/<post_id>) from SQLite.", "Add basic SQLite database initialization (e.g., creating tables from models) and connection logic in app.py or a configuration file.", "Optionally, add routes for basic HTML template rendering if a UI is implied (e.g., in templates/index.html, templates/post_detail.html)."]`;

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: MODEL_NAME,
      contents: `Project Goal: "${projectGoal}"`,
      config: {
        systemInstruction: systemInstruction,
        responseMimeType: "application/json",
        temperature: 0.3,
        topK: 30,
        topP: 0.9,
      }
    });
    
    const parsedData = parseJsonResponse(response.text, "planProjectTasks");
    if (Array.isArray(parsedData) && parsedData.every(item => typeof item === 'string')) {
      return parsedData;
    } else {
      console.error("Parsed JSON is not an array of strings:", parsedData);
      if (typeof response.text === 'string' && response.text.includes('\n')) {
        const lines = response.text.split('\n').map(s => s.trim().replace(/^- \[?x?\]?\s*/, '')).filter(s => s.length > 5);
        if (lines.length > 0) return lines;
      }
      throw new Error("AI returned an invalid task list format. Expected an array of strings.");
    }
  } catch (error) {
    throw handleError(error, "planProjectTasks");
  }
};

const commonFileGenerationSystemInstruction = `You are an expert AI programming assistant specializing in Python, Flask, and SQLite development.
Your primary goal is to generate or modify Python code for Flask applications, using SQLite for database interactions (e.g., through the direct 'sqlite3' library, or an ORM like Flask-SQLAlchemy).
Output ONLY the file blocks. Do NOT add any conversational fluff, explanations, or markdown formatting outside the file blocks.
Ensure your output STRICTLY follows the specified multi-file format, even for single files.
Each file block MUST start with '### \`path/to/filename.ext\`' on its own line (e.g., '### \`app.py\`', '### \`models.py\`', '### \`routes/user_routes.py\`', '### \`static/style.css\`', '### \`templates/index.html\`', '### \`database_setup.sql\`'), followed by a language specifier in triple backticks (e.g., \`\`\`python, \`\`\`html, \`\`\`css, \`\`\`sql), then the code, and finally closing triple backticks.

Correct Format Example for Python/Flask/SQLite:
### \`app.py\`
\`\`\`python
from flask import Flask, request, jsonify
import sqlite3

app = Flask(__name__)
DATABASE = 'mydatabase.db' # Example SQLite database file

def get_db():
    db = getattr(g, '_database', None)
    if db is None:
        db = g._database = sqlite3.connect(DATABASE)
        db.row_factory = sqlite3.Row # Access columns by name
    return db

@app.teardown_appcontext
def close_connection(exception):
    db = getattr(g, '_database', None)
    if db is not None:
        db.close()

# Example: Define a simple route
@app.route('/api/items', methods=['GET'])
def get_items():
    cur = get_db().cursor()
    cur.execute("SELECT * FROM items")
    items = cur.fetchall()
    return jsonify([dict(item) for item in items])

if __name__ == '__main__':
    # Consider adding a function to initialize the DB schema if it doesn't exist
    # init_db() 
    app.run(debug=True)
\`\`\`

### \`models.py\`
\`\`\`python
# This file could contain SQLAlchemy models or helper functions for direct SQLite.
# For example, a function to create tables:
import sqlite3

DATABASE = 'mydatabase.db'

def init_db_schema():
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT
    );
    """)
    # Add other tables here
    conn.commit()
    conn.close()

if __name__ == '__main__':
    init_db_schema()
    print("Database schema initialized.")
\`\`\`

### \`requirements.txt\`
\`\`\`text
Flask>=2.0
# Flask-SQLAlchemy  # If using SQLAlchemy
# other dependencies
\`\`\`

If you are modifying an existing file, provide the COMPLETE new content for that file within this structure.
If the task implies creating a new file (like .py for Python, .html for templates, .css for styles, .sql for raw SQL scripts), do so using the correct path and format.
If no file changes are needed for the given task (e.g., a conceptual discussion or question answered in chat), output an empty response or a single comment block like:
### \`NOTES.md\`
\`\`\`md
// No file changes required for this request. Python/Flask/SQLite considerations: [brief relevant note if any]
\`\`\`
Focus ONLY on the current request. Do not generate tasks or plan ahead unless specifically asked. Prioritize writing functional Python/Flask code, correct SQLite DDL or ORM model definitions, and standard Flask project structure.
Assume standard library 'sqlite3' for database interactions unless 'Flask-SQLAlchemy' or another ORM is explicitly part of the project context or existing files.`;


export const executeDevelopmentTask = async (
  taskDescription: string,
  projectGoal: string,
  existingFiles: AppFile[]
): Promise<string> => {
  if (!ai) throw new Error("Gemini API client not initialized.");

  let fileContext = "This is a new project, no files exist yet, or files were not provided for context. Assume a Python/Flask/SQLite stack unless specified otherwise by the goal or task.";
  if (existingFiles.length > 0) {
    fileContext = "Existing project files (path, and first ~500 chars of content):\n";
    existingFiles.forEach(file => {
      const contentPreview = file.content.length > 500 ? file.content.substring(0, 497) + "..." : file.content;
      fileContext += `### \`${file.name}\`\n\`\`\`${file.language || ''}\n${contentPreview}\n\`\`\`\n\n`;
    });
  }

  const prompt = `Project Goal: "${projectGoal}" (Assume Python/Flask/SQLite unless goal specifies otherwise)
Current Task: "${taskDescription}"

${fileContext}

Based on the project goal, current task, and existing files (if any), generate the necessary code or file modifications for a Python/Flask/SQLite application.
Remember to output ONLY the file blocks in the specified format. Ensure Python files are .py, Flask templates are .html, etc.`;

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: MODEL_NAME,
      contents: prompt,
      config: {
        systemInstruction: commonFileGenerationSystemInstruction,
        temperature: 0.4,
        topP: 0.9,
        topK: 40,
      }
    });

    if (!response.text && !response?.candidates?.[0]?.content?.parts?.[0]?.text) {
      console.warn("Received response from AI (executeDevelopmentTask) but text content is empty.", response);
      return ""; 
    }
    return response.text || "";
  } catch (error) {
    throw handleError(error, `executeDevelopmentTask: ${taskDescription}`);
  }
};

export const executeRefinementTask = async (
  refinementPrompt: string,
  projectGoal: string,
  existingFiles: AppFile[],
  chatHistory?: ConversationTurn[]
): Promise<string> => {
  if (!ai) throw new Error("Gemini API client not initialized.");

  let fileContext = "No existing files provided for context or this is a new aspect of the project. The project is based on Python/Flask/SQLite.";
  if (existingFiles.length > 0) {
    fileContext = "Current project files (path, and first ~500 chars of content) for a Python/Flask/SQLite application:\n";
    existingFiles.forEach(file => {
      const contentPreview = file.content.length > 500 ? file.content.substring(0, 497) + "..." : file.content;
      fileContext += `### \`${file.name}\`\n\`\`\`${file.language || ''}\n${contentPreview}\n\`\`\`\n\n`;
    });
  }
  
  let historyContext = "No prior conversation history for this refinement.";
  if (chatHistory && chatHistory.length > 0) {
    historyContext = "Relevant conversation history (last few turns):\n";
    const recentHistory = chatHistory.slice(-5); 
    recentHistory.forEach(turn => {
      historyContext += `${turn.role === 'user' ? 'User' : 'AI'}: ${turn.content}\n`;
    });
  }

  const prompt = `Original Project Goal: "${projectGoal}" (This is a Python/Flask/SQLite project)
User's Refinement Request: "${refinementPrompt}"

${historyContext}

${fileContext}

Based on the original project goal, the user's refinement request, conversation history, and current project files, generate the necessary code modifications or new files for this Python/Flask/SQLite application.
Output ONLY the file blocks as per the strict format. If you are just answering a question or no code changes are needed, explain in a 'NOTES.md' file block.`;

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: MODEL_NAME,
      contents: prompt,
      config: {
        systemInstruction: commonFileGenerationSystemInstruction,
        temperature: 0.5, 
        topP: 0.9,
        topK: 40,
      }
    });
     if (!response.text && !response?.candidates?.[0]?.content?.parts?.[0]?.text) {
      console.warn("Received response from AI (executeRefinementTask) but text content is empty.", response);
      return "";
    }
    return response.text || "";
  } catch (error) {
    throw handleError(error, `executeRefinementTask: ${refinementPrompt}`);
  }
};