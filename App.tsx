import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Logo } from './components/Logo';
import { AIProject, AITask, AppFile, AIProjectStatus, ConversationTurn } from './types';
import { ProjectSelectionScreen } from './components/ProjectSelectionScreen';
import { ProjectCreationScreen } from './components/ProjectCreationScreen';
import { ProjectWorkspace } from './components/ProjectWorkspace';
import { planProjectTasks, executeDevelopmentTask, executeRefinementTask } from './services/geminiService';
import { v4 as uuidv4 } from 'https://esm.sh/uuid@9.0.1'; 

const parseAiResponseToFiles = (responseText: string): AppFile[] => {
  const filesResult: AppFile[] = [];
  const fileBlockRegex = /### \`(.+?)\`\s*\`\`\`(\w*)\n([\s\S]*?)\n\`\`\`/g;
  let match;
  while ((match = fileBlockRegex.exec(responseText)) !== null) {
    const filePath = match[1].trim();
    const language = match[2].trim() || undefined;
    const codeContent = match[3].trim();
    filesResult.push({ name: filePath, content: codeContent, language, lastModified: new Date().toISOString() });
  }
  if (filesResult.length === 0 && responseText.trim()) {
    filesResult.push({ name: 'output.txt', content: responseText.trim(), language: 'text', lastModified: new Date().toISOString() });
  }
  return filesResult;
};


const App: React.FC = () => {
  const [projects, setProjects] = useState<AIProject[]>(() => {
    const savedProjects = localStorage.getItem('aiProjects');
    return savedProjects ? JSON.parse(savedProjects) : [];
  });
  const [currentProjectId, setCurrentProjectId] = useState<string | null>(() => {
     return localStorage.getItem('currentProjectId') || null;
  });
  const [viewMode, setViewMode] = useState<'project-selection' | 'project-creation' | 'project-workspace'>('project-selection');
  
  const [isLoadingGlobal, setIsLoadingGlobal] = useState<boolean>(false);
  const [globalError, setGlobalError] = useState<string | null>(null);
  const [showApiKeyWarning, setShowApiKeyWarning] = useState<boolean>(false);

  useEffect(() => {
    if (!process.env.API_KEY) {
      setShowApiKeyWarning(true);
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('aiProjects', JSON.stringify(projects));
  }, [projects]);

  useEffect(() => {
    if (currentProjectId) {
      localStorage.setItem('currentProjectId', currentProjectId);
      setViewMode('project-workspace');
    } else {
      localStorage.removeItem('currentProjectId');
      setViewMode('project-selection');
    }
  }, [currentProjectId]);


  const currentProject = useMemo(() => {
    return projects.find(p => p.id === currentProjectId) || null;
  }, [projects, currentProjectId]);

  const updateProject = useCallback((updatedProjectData: Partial<AIProject>) => {
    setProjects(prev => prev.map(p => p.id === (updatedProjectData.id || currentProjectId)
        ? { ...p, ...updatedProjectData, lastUpdatedDate: new Date().toISOString() } 
        : p
    ));
  }, [currentProjectId]);

  const addLogEntry = useCallback((
    logMessage: string, 
    type: 'task' | 'project' | 'refinement' = 'task', 
    projectId?: string, 
    taskIndex?: number
  ) => {
    const targetProjectId = projectId || currentProjectId;
    if (!targetProjectId) return;

    const timestamp = new Date().toLocaleTimeString();
    const fullLogMessage = `${timestamp}: ${logMessage}`;

    setProjects(prevProjects => prevProjects.map(p => {
      if (p.id === targetProjectId) {
        let newTasks = [...p.tasks];
        let newChatHistory = p.chatHistory ? [...p.chatHistory] : [];

        if (type === 'task') {
          const taskIdx = taskIndex !== undefined ? taskIndex : p.currentTaskIndex;
          if (taskIdx >= 0 && taskIdx < newTasks.length) {
            newTasks[taskIdx] = {
              ...newTasks[taskIdx],
              logs: [...newTasks[taskIdx].logs, fullLogMessage]
            };
          } else { // Log against project if task index is invalid (e.g. planning phase)
             newChatHistory.push({ id: uuidv4(), role: 'ai', content: logMessage, timestamp: new Date().toISOString() });
          }
        } else if (type === 'refinement') {
           newChatHistory.push({ id: uuidv4(), role: logMessage.startsWith('[User]') ? 'user' : 'ai', content: logMessage, timestamp: new Date().toISOString() });
        }
        
        return { ...p, tasks: newTasks, chatHistory: newChatHistory, lastUpdatedDate: new Date().toISOString() };
      }
      return p;
    }));
  }, [currentProjectId]);
  
  const handleCreateNewProject = useCallback(async (name: string, goal: string) => {
    setIsLoadingGlobal(true);
    setGlobalError(null);
    const newProjectBase: AIProject = {
      id: uuidv4(),
      name,
      goal,
      tasks: [],
      files: [],
      status: 'planning', // Start in planning
      currentTaskIndex: -1,
      overallProgress: 0,
      creationDate: new Date().toISOString(),
      lastUpdatedDate: new Date().toISOString(),
      errorLog: [],
      chatHistory: [{id: uuidv4(), role: 'ai', content: `Project "${name}" created with goal: "${goal}". Starting task planning.`, timestamp: new Date().toISOString()}]
    };
    setProjects(prev => [...prev, newProjectBase]);
    setCurrentProjectId(newProjectBase.id);
    // No need to setViewMode, useEffect for currentProjectId handles it
    
    addLogEntry(`[AI] Planning tasks for project: ${name}...`, 'project', newProjectBase.id);
    try {
        const taskDescriptions = await planProjectTasks(goal);
        const tasks: AITask[] = taskDescriptions.map(desc => ({
            id: uuidv4(),
            description: desc,
            status: 'pending',
            logs: [],
            attemptCount: 0,
        }));
        updateProject({ id: newProjectBase.id, tasks, status: 'ready-to-develop' });
        addLogEntry(`[AI] Task planning complete. ${tasks.length} tasks created.`, 'project', newProjectBase.id);
    } catch (err) {
        console.error("Failed to plan tasks:", err);
        const errorMsg = err instanceof Error ? err.message : "Unknown error during task planning.";
        updateProject({id: newProjectBase.id, status: 'error', errorLog: [errorMsg]});
        setGlobalError(`Failed to plan tasks for '${name}': ${errorMsg}`);
        addLogEntry(`[AI] Error during task planning: ${errorMsg}`, 'project', newProjectBase.id);
    } finally {
        setIsLoadingGlobal(false);
    }
  }, [updateProject, addLogEntry]);

  const handleSelectProject = useCallback((projectId: string) => {
    setCurrentProjectId(projectId);
  }, []);

  const handleBackToProjects = useCallback(() => {
    setCurrentProjectId(null);
  }, []);

  const processTaskExecution = async (
    projectToProcess: AIProject,
    taskFunction: (taskDescription: string, projectGoal: string, existingFiles: AppFile[], chatHistory?: ConversationTurn[]) => Promise<string>,
    taskSourceDescription: string // Could be task.description or refinement prompt
    ) => {
      const currentTaskInState = projectToProcess.tasks[projectToProcess.currentTaskIndex]; // Assuming task based for now
      addLogEntry(`[AI] Starting: ${taskSourceDescription}`, 'task', projectToProcess.id, projectToProcess.currentTaskIndex);

      const generatedContent = await taskFunction(taskSourceDescription, projectToProcess.goal, projectToProcess.files, projectToProcess.chatHistory);
      const newFiles = parseAiResponseToFiles(generatedContent);

      let updatedProjectFiles = [...projectToProcess.files];
      newFiles.forEach(nf => {
          const existingFileIndex = updatedProjectFiles.findIndex(ef => ef.name === nf.name);
          if (existingFileIndex !== -1) {
              updatedProjectFiles[existingFileIndex] = { ...nf, lastModified: new Date().toISOString() };
          } else {
              updatedProjectFiles.push({ ...nf, lastModified: new Date().toISOString() });
          }
      });
      
      addLogEntry(`[AI] Completed: ${taskSourceDescription}. ${newFiles.length} file(s) affected.`, 'task', projectToProcess.id, projectToProcess.currentTaskIndex);
      if(currentTaskInState) currentTaskInState.generatedContentPreview = newFiles.map(f => f.name).join(', ') || "No files changed.";
      
      return { files: updatedProjectFiles, newFileNames: newFiles.map(f=>f.name) };
  };


  const startAIAutodevelopment = useCallback(async () => {
    if (!currentProject || currentProject.status === 'developing' || currentProject.status === 'planning' || currentProject.status === 'refining') return;

    updateProject({ status: 'developing', errorLog: [] });
    let projectRef = projects.find(p => p.id === currentProject.id)!; 

    addLogEntry(`[AI] Starting automated development for project: ${projectRef.name}`, 'project');

    for (let i = projectRef.currentTaskIndex === -1 ? 0 : projectRef.currentTaskIndex; i < projectRef.tasks.length; i++) {
        projectRef = projects.find(p => p.id === currentProject.id)!; // Refresh projectRef each iteration
        const task = projectRef.tasks[i];
        if (task.status === 'completed' || task.status === 'awaiting-review') continue;

        updateProject({ currentTaskIndex: i, status: 'developing' }); // Ensure status is developing
        projectRef = projects.find(p => p.id === currentProject.id)!; 
        
        const currentTaskInState = projectRef.tasks[i]; // This is the task to modify
        currentTaskInState.status = 'in-progress';
        // Note: addLogEntry will update the specific task log via project update
        updateProject({ tasks: [...projectRef.tasks]}); // Trigger re-render with task status change

        try {
            const {files: updatedFiles} = await processTaskExecution(projectRef, executeDevelopmentTask, task.description);
            
            projectRef = projects.find(p => p.id === currentProject.id)!; // Refresh again
            const taskToUpdate = projectRef.tasks[i];
            taskToUpdate.status = 'completed';
            
            updateProject({
                files: updatedFiles,
                tasks: [...projectRef.tasks], // Send the modified tasks array
                overallProgress: Math.round(((i + 1) / projectRef.tasks.length) * 100)
            });

        } catch (err) {
            console.error(`Error executing task '${task.description}':`, err);
            const errorMsg = err instanceof Error ? err.message : "Unknown error executing task.";
            
            projectRef = projects.find(p => p.id === currentProject.id)!; // Refresh
            const taskToUpdate = projectRef.tasks[i];
            taskToUpdate.status = 'error';
            // addLogEntry handled by processTaskExecution for the error specific to task
            addLogEntry(`[AI] Error: ${errorMsg}`, 'task', projectRef.id, i);


            updateProject({
                status: 'error',
                errorLog: [...projectRef.errorLog, `Task "${task.description}" failed: ${errorMsg}`],
                tasks: [...projectRef.tasks]
            });
            addLogEntry(`[AI] Development paused due to an error in task: ${task.description}.`, 'project');
            return; // Stop processing further tasks
        }
    }
    // After loop finishes
    projectRef = projects.find(p => p.id === currentProject.id)!;
    if (projectRef.status === 'developing') { 
        updateProject({ status: 'review-pending', currentTaskIndex: projectRef.tasks.length > 0 ? projectRef.tasks.length -1 : -1 });
        addLogEntry(`[AI] All tasks completed. Project is pending review.`, 'project');
    }

  }, [currentProject, updateProject, projects, addLogEntry]);
  
  const handleUserReviewConfirmation = useCallback((confirm: boolean, feedback?: string) => {
    if (!currentProject || currentProject.status !== 'review-pending') return;
    const logPrefix = `[User Review] Project "${currentProject.name}" was `;
    if (confirm) {
      updateProject({ status: 'completed' });
      addLogEntry(`${logPrefix} confirmed.`, 'project');
    } else {
      updateProject({ status: 'error', errorLog: [...currentProject.errorLog, `User rejected review. Feedback: ${feedback || 'No feedback provided.'}`] });
      addLogEntry(`${logPrefix} rejected. Feedback: ${feedback || 'No feedback provided.'}`, 'project');
    }
  }, [currentProject, updateProject, addLogEntry]);

  const handleUpdateFileContent = useCallback((filePath: string, newContent: string) => {
    if(!currentProject) return;
    const updatedFiles = currentProject.files.map(f => 
      f.name === filePath ? { ...f, content: newContent, lastModified: new Date().toISOString() } : f
    );
    updateProject({ files: updatedFiles });
    addLogEntry(`[User Edit] File "${filePath}" was modified by the user.`, 'project');
  }, [currentProject, updateProject, addLogEntry]);

  const handleStartRefinement = useCallback(async (refinementPrompt: string) => {
    if (!currentProject || currentProject.status === 'refining') return;

    const userMessage: ConversationTurn = { id: uuidv4(), role: 'user', content: refinementPrompt, timestamp: new Date().toISOString() };
    updateProject({ 
        status: 'refining', 
        errorLog: [], // Clear previous project-level errors
        chatHistory: [...(currentProject.chatHistory || []), userMessage]
    });
    
    let projectRef = projects.find(p => p.id === currentProject.id)!;
    addLogEntry(`[User Request] ${refinementPrompt}`, 'refinement', projectRef.id);
    addLogEntry(`[AI] Processing refinement for project: ${projectRef.name}...`, 'refinement', projectRef.id);

    try {
      const {files: updatedFiles, newFileNames} = await processTaskExecution(projectRef, executeRefinementTask, refinementPrompt);
        
      projectRef = projects.find(p => p.id === currentProject.id)!; // Refresh
      updateProject({
          files: updatedFiles,
          status: 'completed', // Or 'review-pending' or stay 'refining' if more interaction is expected
          chatHistory: [...(projectRef.chatHistory || []), {id: uuidv4(), role: 'ai', content: `Refinement applied. Files affected: ${newFileNames.join(', ') || 'None'}.`, timestamp: new Date().toISOString()}]
      });
      addLogEntry(`[AI] Refinement completed. ${newFileNames.length} file(s) affected. Project is now 'completed'.`, 'refinement', projectRef.id);

    } catch (err) {
        console.error(`Error executing refinement '${refinementPrompt}':`, err);
        const errorMsg = err instanceof Error ? err.message : "Unknown error executing refinement.";
        
        projectRef = projects.find(p => p.id === currentProject.id)!; // Refresh
        updateProject({
            status: 'error', // Keep it in error to allow user to see/retry or new refinement
            errorLog: [...projectRef.errorLog, `Refinement failed: ${errorMsg}`],
            chatHistory: [...(projectRef.chatHistory || []), {id: uuidv4(), role: 'ai', content: `Error during refinement: ${errorMsg}`, timestamp: new Date().toISOString()}]
        });
        addLogEntry(`[AI] Refinement failed: ${errorMsg}`, 'refinement', projectRef.id);
    }
  }, [currentProject, updateProject, projects, addLogEntry]);


  const renderContent = () => {
    if (viewMode === 'project-creation' || (!currentProjectId && viewMode !== 'project-selection')) {
      return <ProjectCreationScreen onCreateProject={handleCreateNewProject} onCancel={handleBackToProjects} isLoading={isLoadingGlobal} error={globalError} />;
    }
    if (viewMode === 'project-workspace' && currentProject) {
      return <ProjectWorkspace 
                project={currentProject} 
                onStartAIDevelopment={startAIAutodevelopment} 
                onUpdateProject={updateProject} // Generic project update for minor things if needed
                onConfirmReview={handleUserReviewConfirmation}
                onBackToProjects={handleBackToProjects}
                onUpdateFileContent={handleUpdateFileContent}
                onStartRefinement={handleStartRefinement}
             />;
    }
    return <ProjectSelectionScreen 
              projects={projects} 
              onSelectProject={handleSelectProject} 
              onCreateNew={() => setViewMode('project-creation')} 
            />;
  };

  return (
    <div className="flex flex-col h-screen max-h-screen overflow-hidden bg-background text-on-surface">
      <header className="w-full p-3 border-b border-gray-700 bg-surface flex items-center justify-between shrink-0">
        <div className="flex items-center space-x-3">
          <Logo className="h-8 w-8 text-primary" />
          <h1 className="text-xl font-semibold text-on-surface">
            Autonomous AI IDE
          </h1>
        </div>
        {currentProject && viewMode === 'project-workspace' && (
           <button 
            onClick={handleBackToProjects}
            className="px-3 py-1.5 text-xs bg-gray-600 hover:bg-gray-500 text-on-surface rounded-md transition-colors"
            >
            Back to Projects
          </button>
        )}
      </header>

      {showApiKeyWarning && (
         <div className="w-full p-3 bg-yellow-500/20 border-b border-yellow-600 text-yellow-200 text-sm shrink-0" role="alert">
          <p><span className="font-semibold">API Key Warning:</span> The Gemini API key (process.env.API_KEY) is not detected. This application requires a valid API key to function. Please ensure it is correctly configured in your environment.</p>
        </div>
      )}
      {isLoadingGlobal && <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"><div className="p-4 bg-surface rounded-lg text-on-surface">Global Loading...</div></div>}
      {globalError && <div className="p-2 bg-error text-white text-center text-sm">{globalError}</div>}

      <main className="flex-grow overflow-hidden">
        {renderContent()}
      </main>
      
      <footer className="p-2 text-center text-xs text-on-surface-muted border-t border-gray-700 bg-surface shrink-0">
        <p>&copy; {new Date().getFullYear()} Autonomous AI IDE. Powered by Gemini.</p>
      </footer>
    </div>
  );
};

export default App;