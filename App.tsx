import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Logo } from './components/Logo';
import { AIProject, AITask, AppFile, AIProjectStatus, ConversationTurn } from './types';
import { ProjectSelectionScreen } from './components/ProjectSelectionScreen';
import { ProjectCreationScreen } from './components/ProjectCreationScreen';
import { ProjectWorkspace } from './components/ProjectWorkspace';
import { AIProviderSettings } from './components/AIProviderSettings';
import { tauriService } from './services/tauriService';
import { v4 as uuidv4 } from 'uuid';

const parseAiResponseToFiles = (responseText: string): AppFile[] => {
  const filesResult: AppFile[] = [];
  const fileBlockRegex = /### \`(.+?)\`\s*\`\`\`(\w*)\n([\s\S]*?)\n\`\`\`/g;
  let match;
  while ((match = fileBlockRegex.exec(responseText)) !== null) {
    const filePath = match[1].trim();
    const language = match[2].trim() || undefined;
    const codeContent = match[3].trim();
    filesResult.push({ name: filePath, content: codeContent, language, lastModified: new Date().toISOString() });
  }
  if (filesResult.length === 0 && responseText.trim()) {
    filesResult.push({ name: 'output.txt', content: responseText.trim(), language: 'text', lastModified: new Date().toISOString() });
  }
  return filesResult;
};


const App: React.FC = () => {
  const [projects, setProjects] = useState<AIProject[]>([]);
  const [currentProjectId, setCurrentProjectId] = useState<string | null>(() => {
     return localStorage.getItem('currentProjectId') || null;
  });
  const [viewMode, setViewMode] = useState<'project-selection' | 'project-creation' | 'project-workspace'>('project-selection');

  const [isLoadingGlobal, setIsLoadingGlobal] = useState<boolean>(true);
  const [globalError, setGlobalError] = useState<string | null>(null);
  const [showProviderSettings, setShowProviderSettings] = useState<boolean>(false);

  // Load projects from Tauri backend on startup
  useEffect(() => {
    const loadProjects = async () => {
      try {
        setIsLoadingGlobal(true);
        const loadedProjects = await tauriService.getAllProjects();
        setProjects(loadedProjects);
      } catch (error) {
        console.error('Failed to load projects:', error);
        setGlobalError('Failed to load projects from database');
      } finally {
        setIsLoadingGlobal(false);
      }
    };

    loadProjects();
  }, []);

  // No longer need to save to localStorage as data is persisted in Tauri backend

  useEffect(() => {
    if (currentProjectId) {
      localStorage.setItem('currentProjectId', currentProjectId);
      setViewMode('project-workspace');
    } else {
      localStorage.removeItem('currentProjectId');
      setViewMode('project-selection');
    }
  }, [currentProjectId]);


  const currentProject = useMemo(() => {
    return projects.find(p => p.id === currentProjectId) || null;
  }, [projects, currentProjectId]);

  // These functions are no longer needed as state management is handled by Tauri backend

  const handleCreateNewProject = useCallback(async (name: string, goal: string) => {
    setIsLoadingGlobal(true);
    setGlobalError(null);

    try {
        const newProject = await tauriService.createProject(name, goal);
        setProjects(prev => [...prev, newProject]);
        setCurrentProjectId(newProject.id);
    } catch (err) {
        console.error("Failed to create project:", err);
        const errorMsg = err instanceof Error ? err.message : "Unknown error during project creation.";
        setGlobalError(`Failed to create project '${name}': ${errorMsg}`);
    } finally {
        setIsLoadingGlobal(false);
    }
  }, []);

  const handleSelectProject = useCallback((projectId: string) => {
    setCurrentProjectId(projectId);
  }, []);

  const handleBackToProjects = useCallback(() => {
    setCurrentProjectId(null);
  }, []);

  // processTaskExecution is no longer needed as it's handled by Tauri backend


  const startAIAutodevelopment = useCallback(async () => {
    if (!currentProject || currentProject.status === 'developing' || currentProject.status === 'planning' || currentProject.status === 'refining') return;

    try {
        const updatedProject = await tauriService.startAIDevelopment(currentProject.id);
        setProjects(prev => prev.map(p => p.id === updatedProject.id ? updatedProject : p));
    } catch (err) {
        console.error("Failed to start AI development:", err);
        const errorMsg = err instanceof Error ? err.message : "Unknown error during AI development.";
        setGlobalError(`Failed to start AI development: ${errorMsg}`);
    }
  }, [currentProject]);

  const handleUserReviewConfirmation = useCallback(async (confirm: boolean, feedback?: string) => {
    if (!currentProject || currentProject.status !== 'review-pending') return;

    try {
        const updatedProject = await tauriService.confirmReview(currentProject.id, confirm, feedback);
        setProjects(prev => prev.map(p => p.id === updatedProject.id ? updatedProject : p));
    } catch (err) {
        console.error("Failed to confirm review:", err);
        const errorMsg = err instanceof Error ? err.message : "Unknown error during review confirmation.";
        setGlobalError(`Failed to confirm review: ${errorMsg}`);
    }
  }, [currentProject]);

  const handleUpdateFileContent = useCallback(async (filePath: string, newContent: string) => {
    if(!currentProject) return;

    try {
        const updatedProject = await tauriService.updateFileContent(currentProject.id, filePath, newContent);
        setProjects(prev => prev.map(p => p.id === updatedProject.id ? updatedProject : p));
    } catch (err) {
        console.error("Failed to update file content:", err);
        const errorMsg = err instanceof Error ? err.message : "Unknown error during file update.";
        setGlobalError(`Failed to update file: ${errorMsg}`);
    }
  }, [currentProject]);

  const handleStartRefinement = useCallback(async (refinementPrompt: string) => {
    if (!currentProject || currentProject.status === 'refining') return;

    try {
        const updatedProject = await tauriService.startRefinement(currentProject.id, refinementPrompt);
        setProjects(prev => prev.map(p => p.id === updatedProject.id ? updatedProject : p));
    } catch (err) {
        console.error("Failed to start refinement:", err);
        const errorMsg = err instanceof Error ? err.message : "Unknown error during refinement.";
        setGlobalError(`Failed to start refinement: ${errorMsg}`);
    }
  }, [currentProject]);


  const renderContent = () => {
    if (viewMode === 'project-creation' || (!currentProjectId && viewMode !== 'project-selection')) {
      return <ProjectCreationScreen onCreateProject={handleCreateNewProject} onCancel={handleBackToProjects} isLoading={isLoadingGlobal} error={globalError} />;
    }
    if (viewMode === 'project-workspace' && currentProject) {
      return <ProjectWorkspace
                project={currentProject}
                onStartAIDevelopment={startAIAutodevelopment}
                onUpdateProject={updateProject} // Generic project update for minor things if needed
                onConfirmReview={handleUserReviewConfirmation}
                onBackToProjects={handleBackToProjects}
                onUpdateFileContent={handleUpdateFileContent}
                onStartRefinement={handleStartRefinement}
             />;
    }
    return <ProjectSelectionScreen
              projects={projects}
              onSelectProject={handleSelectProject}
              onCreateNew={() => setViewMode('project-creation')}
              onOpenSettings={() => setShowProviderSettings(true)}
            />;
  };

  return (
    <div className="flex flex-col h-screen max-h-screen overflow-hidden bg-background text-on-surface">
      <header className="w-full p-3 border-b border-gray-700 bg-surface flex items-center justify-between shrink-0">
        <div className="flex items-center space-x-3">
          <Logo className="h-8 w-8 text-primary" />
          <h1 className="text-xl font-semibold text-on-surface">
            Autonomous AI IDE
          </h1>
        </div>
        {currentProject && viewMode === 'project-workspace' && (
           <button
            onClick={handleBackToProjects}
            className="px-3 py-1.5 text-xs bg-gray-600 hover:bg-gray-500 text-on-surface rounded-md transition-colors"
            >
            Back to Projects
          </button>
        )}
      </header>


      {isLoadingGlobal && <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"><div className="p-4 bg-surface rounded-lg text-on-surface">Global Loading...</div></div>}
      {globalError && <div className="p-2 bg-error text-white text-center text-sm">{globalError}</div>}

      <main className="flex-grow overflow-hidden">
        {renderContent()}
      </main>

      <footer className="p-2 text-center text-xs text-on-surface-muted border-t border-gray-700 bg-surface shrink-0">
        <p>&copy; {new Date().getFullYear()} Autonomous AI IDE. Powered by Multi-AI Providers.</p>
      </footer>

      {/* AI Provider Settings Modal */}
      <AIProviderSettings
        isOpen={showProviderSettings}
        onClose={() => setShowProviderSettings(false)}
      />
    </div>
  );
};

export default App;