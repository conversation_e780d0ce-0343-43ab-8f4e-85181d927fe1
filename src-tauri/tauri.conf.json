{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "Autonomous AI IDE", "version": "0.1.0", "identifier": "com.autonomous-ai-ide.app", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:5173", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "Autonomous AI IDE", "width": 1400, "height": 900, "minWidth": 1000, "minHeight": 700, "resizable": true, "fullscreen": false, "center": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}