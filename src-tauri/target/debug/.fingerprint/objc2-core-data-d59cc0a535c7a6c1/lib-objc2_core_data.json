{"rustc": 657458298742096754, "features": "[\"NSAttributeDescription\", \"NSEntityDescription\", \"NSFetchRequest\", \"NSManagedObjectContext\", \"NSManagedObjectModel\", \"NSPersistentStoreRequest\", \"NSPropertyDescription\", \"bitflags\"]", "declared_features": "[\"CloudKit\", \"CoreDataDefines\", \"CoreDataErrors\", \"NSAtomicStore\", \"NSAtomicStoreCacheNode\", \"NSAttributeDescription\", \"NSBatchDeleteRequest\", \"NSBatchInsertRequest\", \"NSBatchUpdateRequest\", \"NSCompositeAttributeDescription\", \"NSCoreDataCoreSpotlightDelegate\", \"NSCustomMigrationStage\", \"NSDerivedAttributeDescription\", \"NSEntityDescription\", \"NSEntityMapping\", \"NSEntityMigrationPolicy\", \"NSExpressionDescription\", \"NSFetchIndexDescription\", \"NSFetchIndexElementDescription\", \"NSFetchRequest\", \"NSFetchRequestExpression\", \"NSFetchedPropertyDescription\", \"NSFetchedResultsController\", \"NSIncrementalStore\", \"NSIncrementalStoreNode\", \"NSLightweightMigrationStage\", \"NSManagedObject\", \"NSManagedObjectContext\", \"NSManagedObjectID\", \"NSManagedObjectModel\", \"NSManagedObjectModelReference\", \"NSMappingModel\", \"NSMergePolicy\", \"NSMigrationManager\", \"NSMigrationStage\", \"NSPersistentCloudKitContainer\", \"NSPersistentCloudKitContainerEvent\", \"NSPersistentCloudKitContainerEventRequest\", \"NSPersistentCloudKitContainerOptions\", \"NSPersistentContainer\", \"NSPersistentHistoryChange\", \"NSPersistentHistoryChangeRequest\", \"NSPersistentHistoryToken\", \"NSPersistentHistoryTransaction\", \"NSPersistentStore\", \"NSPersistentStoreCoordinator\", \"NSPersistentStoreDescription\", \"NSPersistentStoreRequest\", \"NSPersistentStoreResult\", \"NSPropertyDescription\", \"NSPropertyMapping\", \"NSQueryGenerationToken\", \"NSRelationshipDescription\", \"NSSaveChangesRequest\", \"NSStagedMigrationManager\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"gnustep-1-7\", \"gnustep-1-8\", \"gnustep-1-9\", \"gnustep-2-0\", \"gnustep-2-1\", \"objc2-cloud-kit\", \"std\"]", "target": 13736394510896255602, "profile": 1993307365951621450, "path": 3696281592478596304, "deps": [[4722402946104583944, "bitflags", false, 12724695020299584060], [8503253084725796249, "objc2", false, 8573559342717209133], [9645915364771495524, "objc2_foundation", false, 17880158175138342216]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-core-data-d59cc0a535c7a6c1/dep-lib-objc2_core_data", "checksum": false}}], "rustflags": [], "metadata": 17002138597139157410, "config": 2202906307356721367, "compile_kind": 0}