{"rustc": 657458298742096754, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 3643947551994703751, "profile": 4374887572363265115, "path": 15429893051569579520, "deps": [[10448766010662481490, "num_traits", false, 10075562228691419676], [10633404241517405153, "serde", false, 16537426199877961018], [17958873330977204455, "iana_time_zone", false, 10607652304911443141]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/chrono-d2c0aa7c996785a2/dep-lib-chrono", "checksum": false}}], "rustflags": [], "metadata": 9803565982372010724, "config": 2202906307356721367, "compile_kind": 0}