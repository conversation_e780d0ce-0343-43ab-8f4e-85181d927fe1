cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=/Users/<USER>/Downloads/autonomous-ai-ide/src-tauri/target/debug/build/tauri-plugin-dialog-1f636fcc02bf21e3/out/tauri-plugin-dialog-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tauri-plugin-dialog-2.2.2/api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
