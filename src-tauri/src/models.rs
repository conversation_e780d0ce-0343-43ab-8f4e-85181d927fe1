use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AppFile {
    pub name: String,
    pub content: String,
    pub language: Option<String>,
    pub last_modified: Option<DateTime<Utc>>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub enum AITaskStatus {
    #[serde(rename = "pending")]
    Pending,
    #[serde(rename = "in-progress")]
    InProgress,
    #[serde(rename = "completed")]
    Completed,
    #[serde(rename = "error")]
    Error,
    #[serde(rename = "awaiting-review")]
    AwaitingReview,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AITask {
    pub id: Uuid,
    pub description: String,
    pub status: AITaskStatus,
    pub logs: Vec<String>,
    pub generated_content_preview: Option<String>,
    pub attempt_count: i32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum AIProjectStatus {
    #[serde(rename = "new")]
    New,
    #[serde(rename = "planning")]
    Planning,
    #[serde(rename = "ready-to-develop")]
    ReadyToDevelop,
    #[serde(rename = "developing")]
    Developing,
    #[serde(rename = "paused")]
    Paused,
    #[serde(rename = "completed")]
    Completed,
    #[serde(rename = "error")]
    Error,
    #[serde(rename = "review-pending")]
    ReviewPending,
    #[serde(rename = "refining")]
    Refining,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationTurn {
    pub id: Uuid,
    pub role: String, // "user" or "ai"
    pub content: String,
    pub timestamp: DateTime<Utc>,
    pub related_file: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIProject {
    pub id: Uuid,
    pub name: String,
    pub goal: String,
    pub tasks: Vec<AITask>,
    pub files: Vec<AppFile>,
    pub status: AIProjectStatus,
    pub current_task_index: i32,
    pub overall_progress: i32,
    pub creation_date: DateTime<Utc>,
    pub last_updated_date: DateTime<Utc>,
    pub error_log: Vec<String>,
    pub chat_history: Option<Vec<ConversationTurn>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileSystemNode {
    pub id: String,
    pub name: String,
    pub path: String,
    pub node_type: String, // "file" or "folder"
    pub children: Option<Vec<FileSystemNode>>,
    pub content: Option<String>,
    pub language: Option<String>,
    pub last_modified: Option<DateTime<Utc>>,
}

// Request/Response DTOs for Tauri commands
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateProjectRequest {
    pub name: String,
    pub goal: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateFileRequest {
    pub project_id: Uuid,
    pub file_path: String,
    pub content: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StartRefinementRequest {
    pub project_id: Uuid,
    pub prompt: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ReviewConfirmationRequest {
    pub project_id: Uuid,
    pub confirm: bool,
    pub feedback: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GeminiRequest {
    pub prompt: String,
    pub context: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GeminiResponse {
    pub content: String,
}
