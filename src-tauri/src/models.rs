use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum AIProvider {
    #[serde(rename = "gemini")]
    Gemini,
    #[serde(rename = "openrouter")]
    OpenRouter,
    #[serde(rename = "openai")]
    OpenAI,
    #[serde(rename = "anthropic")]
    Anthropic,
    #[serde(rename = "ollama")]
    Ollama,
    #[serde(rename = "lmstudio")]
    LMStudio,
    #[serde(rename = "textgen")]
    TextGenWebUI,
    #[serde(rename = "koboldcpp")]
    KoboldCpp,
    #[serde(rename = "llamacpp")]
    LlamaCpp,
    #[serde(rename = "custom")]
    Custom,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIModel {
    pub id: String,
    pub name: String,
    pub provider: AIProvider,
    pub max_tokens: Option<u32>,
    pub cost_per_token: Option<f64>,
    pub supports_streaming: bool,
    pub context_window: Option<u32>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AIProviderConfig {
    pub provider: AIProvider,
    pub api_key: Option<String>,
    pub base_url: Option<String>,
    pub model_id: String,
    pub enabled: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppFile {
    pub name: String,
    pub content: String,
    pub language: Option<String>,
    #[serde(rename = "lastModified")]
    pub last_modified: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AITaskStatus {
    #[serde(rename = "pending")]
    Pending,
    #[serde(rename = "in-progress")]
    InProgress,
    #[serde(rename = "completed")]
    Completed,
    #[serde(rename = "error")]
    Error,
    #[serde(rename = "awaiting-review")]
    AwaitingReview,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AITask {
    pub id: Uuid,
    pub description: String,
    pub status: AITaskStatus,
    pub logs: Vec<String>,
    #[serde(rename = "generatedContentPreview")]
    pub generated_content_preview: Option<String>,
    #[serde(rename = "attemptCount")]
    pub attempt_count: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AIProjectStatus {
    #[serde(rename = "new")]
    New,
    #[serde(rename = "planning")]
    Planning,
    #[serde(rename = "ready-to-develop")]
    ReadyToDevelop,
    #[serde(rename = "developing")]
    Developing,
    #[serde(rename = "paused")]
    Paused,
    #[serde(rename = "completed")]
    Completed,
    #[serde(rename = "error")]
    Error,
    #[serde(rename = "review-pending")]
    ReviewPending,
    #[serde(rename = "refining")]
    Refining,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationTurn {
    pub id: Uuid,
    pub role: String, // "user" or "ai"
    pub content: String,
    pub timestamp: DateTime<Utc>,
    #[serde(rename = "relatedFile")]
    pub related_file: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIProject {
    pub id: Uuid,
    pub name: String,
    pub goal: String,
    pub tasks: Vec<AITask>,
    pub files: Vec<AppFile>,
    pub status: AIProjectStatus,
    #[serde(rename = "currentTaskIndex")]
    pub current_task_index: i32,
    #[serde(rename = "overallProgress")]
    pub overall_progress: i32,
    #[serde(rename = "creationDate")]
    pub creation_date: DateTime<Utc>,
    #[serde(rename = "lastUpdatedDate")]
    pub last_updated_date: DateTime<Utc>,
    #[serde(rename = "errorLog")]
    pub error_log: Vec<String>,
    #[serde(rename = "chatHistory")]
    pub chat_history: Option<Vec<ConversationTurn>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileSystemNode {
    pub id: String,
    pub name: String,
    pub path: String,
    pub node_type: String, // "file" or "folder"
    pub children: Option<Vec<FileSystemNode>>,
    pub content: Option<String>,
    pub language: Option<String>,
    pub last_modified: Option<DateTime<Utc>>,
}

// Request/Response DTOs for Tauri commands
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateProjectRequest {
    pub name: String,
    pub goal: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateFileRequest {
    pub project_id: Uuid,
    pub file_path: String,
    pub content: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StartRefinementRequest {
    pub project_id: Uuid,
    pub prompt: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ReviewConfirmationRequest {
    pub project_id: Uuid,
    pub confirm: bool,
    pub feedback: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GeminiRequest {
    pub prompt: String,
    pub context: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GeminiResponse {
    pub content: String,
}

// AI Provider Management
#[derive(Debug, Serialize, Deserialize)]
pub struct GetAvailableModelsRequest {
    pub provider: Option<AIProvider>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SetActiveModelRequest {
    pub provider: AIProvider,
    pub model_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateProviderConfigRequest {
    pub config: AIProviderConfig,
}
