use std::env;
use tauri::Manager;

mod models;
mod database;
mod ai_service;
mod commands;

use database::Database;
use ai_service::AIService;

pub struct AppState {
    pub database: Database,
    pub ai_service: AIService,
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_log::Builder::default().build())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .setup(|app| {
            // Get API key from environment
            let api_key = env::var("GEMINI_API_KEY")
                .or_else(|_| env::var("API_KEY"))
                .unwrap_or_else(|_| {
                    log::warn!("No API key found in environment variables GEMINI_API_KEY or API_KEY");
                    String::new()
                });

            // Initialize database
            let app_data_dir = app.path().app_data_dir()
                .expect("Failed to get app data directory");

            std::fs::create_dir_all(&app_data_dir)
                .expect("Failed to create app data directory");

            let db_path = app_data_dir.join("autonomous_ai_ide.db");
            let database_url = format!("sqlite://{}?mode=rwc", db_path.to_string_lossy());

            // Clone the app handle for the async task
            let app_handle = app.handle().clone();

            // Initialize services asynchronously
            tauri::async_runtime::spawn(async move {
                match Database::new(&database_url).await {
                    Ok(database) => {
                        let ai_service = AIService::new(api_key);
                        let state = AppState { database, ai_service };
                        app_handle.manage(state);
                        log::info!("Application initialized successfully");
                    }
                    Err(e) => {
                        log::error!("Failed to initialize database: {}", e);
                        std::process::exit(1);
                    }
                }
            });

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            commands::get_all_projects,
            commands::get_project,
            commands::create_project,
            commands::start_ai_development,
            commands::update_file_content,
            commands::start_refinement,
            commands::confirm_review,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
