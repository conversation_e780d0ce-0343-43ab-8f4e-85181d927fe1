use std::env;
use tauri::Manager;

mod models;
mod database;
mod ai_service;
mod multi_ai_service;
mod commands;

use database::Database;
use multi_ai_service::MultiAIService;

pub struct AppState {
    pub database: Database,
    pub ai_service: MultiAIService,
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_log::Builder::default().build())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .setup(|app| {
            // Initialize database
            let app_data_dir = app.path().app_data_dir()
                .expect("Failed to get app data directory");

            std::fs::create_dir_all(&app_data_dir)
                .expect("Failed to create app data directory");

            let db_path = app_data_dir.join("autonomous_ai_ide.db");
            let database_url = format!("sqlite://{}?mode=rwc", db_path.to_string_lossy());

            // Clone the app handle for the async task
            let app_handle = app.handle().clone();

            // Initialize services asynchronously
            tauri::async_runtime::spawn(async move {
                match Database::new(&database_url).await {
                    Ok(database) => {
                        let ai_service = MultiAIService::new();
                        let state = AppState { database, ai_service };
                        app_handle.manage(state);
                        log::info!("Application initialized successfully");
                    }
                    Err(e) => {
                        log::error!("Failed to initialize database: {}", e);
                        std::process::exit(1);
                    }
                }
            });

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            commands::get_all_projects,
            commands::get_project,
            commands::create_project,
            commands::start_ai_development,
            commands::update_file_content,
            commands::start_refinement,
            commands::confirm_review,
            commands::get_available_models,
            commands::get_enabled_providers,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
