use anyhow::{Result, anyhow};
use reqwest::Client;
use serde_json::{json, Value};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use crate::models::*;

pub struct MultiAIService {
    client: Client,
    providers: HashMap<AIProvider, AIProviderConfig>,
    active_provider: AIProvider,
    available_models: Vec<AIModel>,
    cancellation_tokens: Arc<Mutex<HashMap<String, bool>>>,
}

impl MultiAIService {
    pub fn new() -> Self {
        let mut providers = HashMap::new();

        // 默认配置
        providers.insert(AIProvider::Gemini, AIProviderConfig {
            provider: AIProvider::Gemini,
            api_key: std::env::var("GEMINI_API_KEY").ok(),
            base_url: Some("https://generativelanguage.googleapis.com/v1beta".to_string()),
            model_id: "gemini-1.5-flash-latest".to_string(),
            enabled: std::env::var("GEMINI_API_KEY").is_ok(),
        });

        providers.insert(AIProvider::OpenRouter, AIProviderConfig {
            provider: AIProvider::OpenRouter,
            api_key: std::env::var("OPENROUTER_API_KEY").ok(),
            base_url: Some("https://openrouter.ai/api/v1".to_string()),
            model_id: "meta-llama/llama-3.2-3b-instruct:free".to_string(),
            enabled: std::env::var("OPENROUTER_API_KEY").is_ok(),
        });

        providers.insert(AIProvider::OpenAI, AIProviderConfig {
            provider: AIProvider::OpenAI,
            api_key: std::env::var("OPENAI_API_KEY").ok(),
            base_url: Some("https://api.openai.com/v1".to_string()),
            model_id: "gpt-3.5-turbo".to_string(),
            enabled: std::env::var("OPENAI_API_KEY").is_ok(),
        });

        providers.insert(AIProvider::Ollama, AIProviderConfig {
            provider: AIProvider::Ollama,
            api_key: None,
            base_url: Some("http://localhost:11434".to_string()),
            model_id: "llama3.2:3b".to_string(),
            enabled: true, // Ollama 不需要 API key
        });

        providers.insert(AIProvider::LMStudio, AIProviderConfig {
            provider: AIProvider::LMStudio,
            api_key: None,
            base_url: Some("http://localhost:1234".to_string()),
            model_id: "local-model".to_string(),
            enabled: true, // LM Studio 不需要 API key
        });

        providers.insert(AIProvider::TextGenWebUI, AIProviderConfig {
            provider: AIProvider::TextGenWebUI,
            api_key: None,
            base_url: Some("http://localhost:5000".to_string()),
            model_id: "local-model".to_string(),
            enabled: true, // Text Generation WebUI 不需要 API key
        });

        providers.insert(AIProvider::KoboldCpp, AIProviderConfig {
            provider: AIProvider::KoboldCpp,
            api_key: None,
            base_url: Some("http://localhost:5001".to_string()),
            model_id: "local-model".to_string(),
            enabled: true, // KoboldCpp 不需要 API key
        });

        providers.insert(AIProvider::LlamaCpp, AIProviderConfig {
            provider: AIProvider::LlamaCpp,
            api_key: None,
            base_url: Some("http://localhost:8080".to_string()),
            model_id: "local-model".to_string(),
            enabled: true, // llama.cpp server 不需要 API key
        });

        providers.insert(AIProvider::Custom, AIProviderConfig {
            provider: AIProvider::Custom,
            api_key: std::env::var("CUSTOM_API_KEY").ok(),
            base_url: std::env::var("CUSTOM_BASE_URL").ok(),
            model_id: std::env::var("CUSTOM_MODEL_ID").unwrap_or_else(|_| "custom-model".to_string()),
            enabled: std::env::var("CUSTOM_BASE_URL").is_ok(),
        });

        // 选择第一个可用的供应商
        let active_provider = providers.values()
            .find(|config| config.enabled)
            .map(|config| config.provider.clone())
            .unwrap_or(AIProvider::Gemini);

        Self {
            client: Client::new(),
            providers,
            active_provider,
            available_models: Self::get_default_models(),
            cancellation_tokens: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    fn get_default_models() -> Vec<AIModel> {
        vec![
            // Gemini Models
            AIModel {
                id: "gemini-1.5-flash-latest".to_string(),
                name: "Gemini 1.5 Flash".to_string(),
                provider: AIProvider::Gemini,
                max_tokens: Some(8192),
                cost_per_token: Some(0.000001),
                supports_streaming: true,
                context_window: Some(1000000),
            },
            AIModel {
                id: "gemini-1.5-pro-latest".to_string(),
                name: "Gemini 1.5 Pro".to_string(),
                provider: AIProvider::Gemini,
                max_tokens: Some(8192),
                cost_per_token: Some(0.000005),
                supports_streaming: true,
                context_window: Some(2000000),
            },

            // OpenRouter Free Models
            AIModel {
                id: "meta-llama/llama-3.2-3b-instruct:free".to_string(),
                name: "Llama 3.2 3B (Free)".to_string(),
                provider: AIProvider::OpenRouter,
                max_tokens: Some(4096),
                cost_per_token: Some(0.0),
                supports_streaming: true,
                context_window: Some(131072),
            },
            AIModel {
                id: "meta-llama/llama-3.2-1b-instruct:free".to_string(),
                name: "Llama 3.2 1B (Free)".to_string(),
                provider: AIProvider::OpenRouter,
                max_tokens: Some(4096),
                cost_per_token: Some(0.0),
                supports_streaming: true,
                context_window: Some(131072),
            },
            AIModel {
                id: "microsoft/phi-3-mini-128k-instruct:free".to_string(),
                name: "Phi-3 Mini (Free)".to_string(),
                provider: AIProvider::OpenRouter,
                max_tokens: Some(4096),
                cost_per_token: Some(0.0),
                supports_streaming: true,
                context_window: Some(128000),
            },
            AIModel {
                id: "google/gemma-2-9b-it:free".to_string(),
                name: "Gemma 2 9B (Free)".to_string(),
                provider: AIProvider::OpenRouter,
                max_tokens: Some(8192),
                cost_per_token: Some(0.0),
                supports_streaming: true,
                context_window: Some(8192),
            },

            // OpenAI Models
            AIModel {
                id: "gpt-3.5-turbo".to_string(),
                name: "GPT-3.5 Turbo".to_string(),
                provider: AIProvider::OpenAI,
                max_tokens: Some(4096),
                cost_per_token: Some(0.000002),
                supports_streaming: true,
                context_window: Some(16385),
            },
            AIModel {
                id: "gpt-4o-mini".to_string(),
                name: "GPT-4o Mini".to_string(),
                provider: AIProvider::OpenAI,
                max_tokens: Some(16384),
                cost_per_token: Some(0.00000015),
                supports_streaming: true,
                context_window: Some(128000),
            },

            // Ollama Models (本地)
            AIModel {
                id: "llama3.2:3b".to_string(),
                name: "Llama 3.2 3B (Ollama)".to_string(),
                provider: AIProvider::Ollama,
                max_tokens: Some(4096),
                cost_per_token: Some(0.0),
                supports_streaming: true,
                context_window: Some(131072),
            },
            AIModel {
                id: "qwen2.5:3b".to_string(),
                name: "Qwen 2.5 3B (Ollama)".to_string(),
                provider: AIProvider::Ollama,
                max_tokens: Some(4096),
                cost_per_token: Some(0.0),
                supports_streaming: true,
                context_window: Some(32768),
            },
            AIModel {
                id: "codellama:7b".to_string(),
                name: "Code Llama 7B (Ollama)".to_string(),
                provider: AIProvider::Ollama,
                max_tokens: Some(4096),
                cost_per_token: Some(0.0),
                supports_streaming: true,
                context_window: Some(16384),
            },
            AIModel {
                id: "deepseek-coder:6.7b".to_string(),
                name: "DeepSeek Coder 6.7B (Ollama)".to_string(),
                provider: AIProvider::Ollama,
                max_tokens: Some(4096),
                cost_per_token: Some(0.0),
                supports_streaming: true,
                context_window: Some(16384),
            },

            // LM Studio Models
            AIModel {
                id: "local-model".to_string(),
                name: "Local Model (LM Studio)".to_string(),
                provider: AIProvider::LMStudio,
                max_tokens: Some(4096),
                cost_per_token: Some(0.0),
                supports_streaming: true,
                context_window: Some(8192),
            },

            // Text Generation WebUI Models
            AIModel {
                id: "local-model".to_string(),
                name: "Local Model (Text Gen WebUI)".to_string(),
                provider: AIProvider::TextGenWebUI,
                max_tokens: Some(4096),
                cost_per_token: Some(0.0),
                supports_streaming: true,
                context_window: Some(8192),
            },

            // KoboldCpp Models
            AIModel {
                id: "local-model".to_string(),
                name: "Local Model (KoboldCpp)".to_string(),
                provider: AIProvider::KoboldCpp,
                max_tokens: Some(4096),
                cost_per_token: Some(0.0),
                supports_streaming: true,
                context_window: Some(8192),
            },

            // llama.cpp Server Models
            AIModel {
                id: "local-model".to_string(),
                name: "Local Model (llama.cpp)".to_string(),
                provider: AIProvider::LlamaCpp,
                max_tokens: Some(4096),
                cost_per_token: Some(0.0),
                supports_streaming: true,
                context_window: Some(8192),
            },

            // Custom Models
            AIModel {
                id: "custom-model".to_string(),
                name: "Custom Local Model".to_string(),
                provider: AIProvider::Custom,
                max_tokens: Some(4096),
                cost_per_token: Some(0.0),
                supports_streaming: true,
                context_window: Some(8192),
            },
        ]
    }

    pub fn get_available_models(&self, provider_filter: Option<AIProvider>) -> Vec<AIModel> {
        match provider_filter {
            Some(provider) => self.available_models.iter()
                .filter(|model| model.provider == provider)
                .cloned()
                .collect(),
            None => self.available_models.clone(),
        }
    }

    pub fn get_enabled_providers(&self) -> Vec<AIProviderConfig> {
        self.providers.values()
            .filter(|config| config.enabled)
            .cloned()
            .collect()
    }

    pub fn set_active_provider(&mut self, provider: AIProvider, model_id: String) -> Result<()> {
        if let Some(config) = self.providers.get_mut(&provider) {
            if !config.enabled {
                return Err(anyhow!("Provider {:?} is not enabled", provider));
            }
            config.model_id = model_id;
            self.active_provider = provider;
            Ok(())
        } else {
            Err(anyhow!("Provider {:?} not found", provider))
        }
    }

    pub fn update_provider_config(&mut self, config: AIProviderConfig) {
        self.providers.insert(config.provider.clone(), config);
    }

    pub async fn cancel_operation(&self, operation_id: &str) -> Result<()> {
        let mut tokens = self.cancellation_tokens.lock().await;
        tokens.insert(operation_id.to_string(), true);
        log::info!("Cancellation requested for operation: {}", operation_id);
        Ok(())
    }

    pub async fn is_cancelled(&self, operation_id: &str) -> bool {
        let tokens = self.cancellation_tokens.lock().await;
        tokens.get(operation_id).copied().unwrap_or(false)
    }

    pub async fn cleanup_operation(&self, operation_id: &str) {
        let mut tokens = self.cancellation_tokens.lock().await;
        tokens.remove(operation_id);
    }

    pub async fn plan_project_tasks(&self, project_goal: &str) -> Result<Vec<String>> {
        let prompt = format!(
            r#"You are an AI project planner. Given a project goal, break it down into specific, actionable development tasks.

Project Goal: {}

Please provide a JSON array of task descriptions. Each task should be:
1. Specific and actionable
2. Focused on a single aspect of development
3. Ordered logically (dependencies considered)
4. Suitable for implementation by an AI coding assistant

Example format:
["Set up project structure and dependencies", "Create database schema", "Implement user authentication", "Build main application logic", "Add error handling and logging", "Create user interface", "Write tests", "Add documentation"]

Respond with ONLY the JSON array, no additional text."#,
            project_goal
        );

        let response = self.call_ai_api(&prompt).await?;
        let tasks: Vec<String> = serde_json::from_str(&response)
            .map_err(|_| anyhow!("Failed to parse task list from AI response"))?;

        Ok(tasks)
    }

    pub async fn execute_development_task(
        &self,
        task_description: &str,
        project_goal: &str,
        existing_files: &[AppFile],
        _chat_history: Option<&[ConversationTurn]>,
    ) -> Result<String> {
        let file_context = if existing_files.is_empty() {
            "This is a new project, no files exist yet, or files were not provided for context. Assume a Python/Flask/SQLite stack unless specified otherwise by the goal or task.".to_string()
        } else {
            let mut context = "Existing project files (path, and first ~500 chars of content):\n".to_string();
            for file in existing_files {
                let content_preview = if file.content.len() > 500 {
                    format!("{}...", &file.content[..497])
                } else {
                    file.content.clone()
                };
                context.push_str(&format!(
                    "### `{}`\n```{}\n{}\n```\n\n",
                    file.name,
                    file.language.as_deref().unwrap_or(""),
                    content_preview
                ));
            }
            context
        };

        let prompt = format!(
            r#"You are an AI software developer. Your task is to implement the following development task for a project.

**Project Goal:** {}

**Current Task:** {}

**Context:**
{}

**Instructions:**
1. Implement the task by creating or modifying files as needed
2. Follow best practices for the technology stack
3. Include proper error handling and logging
4. Add comments where necessary
5. Ensure code is production-ready

**Output Format:**
For each file you create or modify, use this exact format:

### `path/to/file.ext`
```language
file content here
```

If you need to explain your implementation, add a brief comment at the end.

Begin implementation:"#,
            project_goal, task_description, file_context
        );

        self.call_ai_api(&prompt).await
    }

    pub async fn execute_refinement_task(
        &self,
        refinement_prompt: &str,
        project_goal: &str,
        existing_files: &[AppFile],
        chat_history: Option<&[ConversationTurn]>,
    ) -> Result<String> {
        let file_context = if existing_files.is_empty() {
            "No existing files provided for context or this is a new aspect of the project.".to_string()
        } else {
            let mut context = "Current project files:\n".to_string();
            for file in existing_files {
                let content_preview = if file.content.len() > 500 {
                    format!("{}...", &file.content[..497])
                } else {
                    file.content.clone()
                };
                context.push_str(&format!(
                    "### `{}`\n```{}\n{}\n```\n\n",
                    file.name,
                    file.language.as_deref().unwrap_or(""),
                    content_preview
                ));
            }
            context
        };

        let chat_context = if let Some(history) = chat_history {
            let mut context = "Previous conversation:\n".to_string();
            for turn in history.iter().rev().take(10).rev() {
                context.push_str(&format!("{}: {}\n", turn.role, turn.content));
            }
            context
        } else {
            "No previous conversation history.".to_string()
        };

        let prompt = format!(
            r#"You are an AI software developer working on project refinements and improvements.

**Project Goal:** {}

**User Request:** {}

**Context:**
{}

**Conversation History:**
{}

**Instructions:**
1. Understand the user's request and implement the necessary changes
2. Modify existing files or create new ones as needed
3. Maintain consistency with the existing codebase
4. Follow best practices and ensure quality
5. Provide clear explanations for significant changes

**Output Format:**
For each file you create or modify, use this exact format:

### `path/to/file.ext`
```language
file content here
```

If you need to explain your changes, add a brief comment at the end.

Begin refinement:"#,
            project_goal, refinement_prompt, file_context, chat_context
        );

        self.call_ai_api(&prompt).await
    }

    async fn call_ai_api(&self, prompt: &str) -> Result<String> {
        let config = self.providers.get(&self.active_provider)
            .ok_or_else(|| anyhow!("Active provider not configured"))?;

        if !config.enabled {
            return Err(anyhow!("Active provider is not enabled"));
        }

        match self.active_provider {
            AIProvider::Gemini => self.call_gemini_api(prompt, config).await,
            AIProvider::OpenRouter => self.call_openrouter_api(prompt, config).await,
            AIProvider::OpenAI => self.call_openai_api(prompt, config).await,
            AIProvider::Ollama => self.call_ollama_api(prompt, config).await,
            AIProvider::Anthropic => self.call_anthropic_api(prompt, config).await,
            AIProvider::LMStudio => self.call_openai_compatible_api(prompt, config).await,
            AIProvider::TextGenWebUI => self.call_textgen_api(prompt, config).await,
            AIProvider::KoboldCpp => self.call_kobold_api(prompt, config).await,
            AIProvider::LlamaCpp => self.call_openai_compatible_api(prompt, config).await,
            AIProvider::Custom => self.call_openai_compatible_api(prompt, config).await,
        }
    }

    async fn call_gemini_api(&self, prompt: &str, config: &AIProviderConfig) -> Result<String> {
        let api_key = config.api_key.as_ref()
            .ok_or_else(|| anyhow!("Gemini API key not configured"))?;

        let url = format!("{}/models/{}:generateContent",
            config.base_url.as_ref().unwrap(),
            config.model_id
        );

        let payload = json!({
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.7,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 8192
            }
        });

        let response = self.client
            .post(&url)
            .header("Content-Type", "application/json")
            .header("x-goog-api-key", api_key)
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow!("Gemini API error: {}", error_text));
        }

        let response_json: Value = response.json().await?;

        let content = response_json
            .get("candidates")
            .and_then(|c| c.get(0))
            .and_then(|c| c.get("content"))
            .and_then(|c| c.get("parts"))
            .and_then(|p| p.get(0))
            .and_then(|p| p.get("text"))
            .and_then(|t| t.as_str())
            .ok_or_else(|| anyhow!("Invalid response format from Gemini API"))?;

        Ok(content.to_string())
    }

    async fn call_openrouter_api(&self, prompt: &str, config: &AIProviderConfig) -> Result<String> {
        let api_key = config.api_key.as_ref()
            .ok_or_else(|| anyhow!("OpenRouter API key not configured"))?;

        let url = format!("{}/chat/completions", config.base_url.as_ref().unwrap());

        let payload = json!({
            "model": config.model_id,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 4096
        });

        let response = self.client
            .post(&url)
            .header("Content-Type", "application/json")
            .header("Authorization", format!("Bearer {}", api_key))
            .header("HTTP-Referer", "https://autonomous-ai-ide.app")
            .header("X-Title", "Autonomous AI IDE")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow!("OpenRouter API error: {}", error_text));
        }

        let response_json: Value = response.json().await?;

        let content = response_json
            .get("choices")
            .and_then(|c| c.get(0))
            .and_then(|c| c.get("message"))
            .and_then(|m| m.get("content"))
            .and_then(|t| t.as_str())
            .ok_or_else(|| anyhow!("Invalid response format from OpenRouter API"))?;

        Ok(content.to_string())
    }

    async fn call_openai_api(&self, prompt: &str, config: &AIProviderConfig) -> Result<String> {
        let api_key = config.api_key.as_ref()
            .ok_or_else(|| anyhow!("OpenAI API key not configured"))?;

        let url = format!("{}/chat/completions", config.base_url.as_ref().unwrap());

        let payload = json!({
            "model": config.model_id,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 4096
        });

        let response = self.client
            .post(&url)
            .header("Content-Type", "application/json")
            .header("Authorization", format!("Bearer {}", api_key))
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow!("OpenAI API error: {}", error_text));
        }

        let response_json: Value = response.json().await?;

        let content = response_json
            .get("choices")
            .and_then(|c| c.get(0))
            .and_then(|c| c.get("message"))
            .and_then(|m| m.get("content"))
            .and_then(|t| t.as_str())
            .ok_or_else(|| anyhow!("Invalid response format from OpenAI API"))?;

        Ok(content.to_string())
    }

    async fn call_ollama_api(&self, prompt: &str, config: &AIProviderConfig) -> Result<String> {
        let url = format!("{}/api/generate", config.base_url.as_ref().unwrap());

        let payload = json!({
            "model": config.model_id,
            "prompt": prompt,
            "stream": false,
            "options": {
                "temperature": 0.7,
                "num_predict": 4096
            }
        });

        let response = self.client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow!("Ollama API error: {}", error_text));
        }

        let response_json: Value = response.json().await?;

        let content = response_json
            .get("response")
            .and_then(|t| t.as_str())
            .ok_or_else(|| anyhow!("Invalid response format from Ollama API"))?;

        Ok(content.to_string())
    }

    async fn call_anthropic_api(&self, prompt: &str, config: &AIProviderConfig) -> Result<String> {
        let api_key = config.api_key.as_ref()
            .ok_or_else(|| anyhow!("Anthropic API key not configured"))?;

        let url = "https://api.anthropic.com/v1/messages";

        let payload = json!({
            "model": config.model_id,
            "max_tokens": 4096,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        });

        let response = self.client
            .post(url)
            .header("Content-Type", "application/json")
            .header("x-api-key", api_key)
            .header("anthropic-version", "2023-06-01")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow!("Anthropic API error: {}", error_text));
        }

        let response_json: Value = response.json().await?;

        let content = response_json
            .get("content")
            .and_then(|c| c.get(0))
            .and_then(|c| c.get("text"))
            .and_then(|t| t.as_str())
            .ok_or_else(|| anyhow!("Invalid response format from Anthropic API"))?;

        Ok(content.to_string())
    }

    // OpenAI 兼容的 API (LM Studio, llama.cpp server, Custom)
    async fn call_openai_compatible_api(&self, prompt: &str, config: &AIProviderConfig) -> Result<String> {
        let url = format!("{}/v1/chat/completions", config.base_url.as_ref().unwrap());

        let payload = json!({
            "model": config.model_id,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 4096
        });

        let mut request = self.client
            .post(&url)
            .header("Content-Type", "application/json");

        // 如果有 API 密钥，添加授权头
        if let Some(api_key) = &config.api_key {
            request = request.header("Authorization", format!("Bearer {}", api_key));
        }

        let response = request
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow!("{:?} API error: {}", config.provider, error_text));
        }

        let response_json: Value = response.json().await?;

        let content = response_json
            .get("choices")
            .and_then(|c| c.get(0))
            .and_then(|c| c.get("message"))
            .and_then(|m| m.get("content"))
            .and_then(|t| t.as_str())
            .ok_or_else(|| anyhow!("Invalid response format from {:?} API", config.provider))?;

        Ok(content.to_string())
    }

    // Text Generation WebUI API
    async fn call_textgen_api(&self, prompt: &str, config: &AIProviderConfig) -> Result<String> {
        let url = format!("{}/api/v1/generate", config.base_url.as_ref().unwrap());

        let payload = json!({
            "prompt": prompt,
            "max_new_tokens": 4096,
            "temperature": 0.7,
            "top_p": 0.9,
            "typical_p": 1,
            "repetition_penalty": 1.1,
            "encoder_repetition_penalty": 1.0,
            "top_k": 40,
            "min_length": 0,
            "no_repeat_ngram_size": 0,
            "num_beams": 1,
            "penalty_alpha": 0,
            "length_penalty": 1,
            "early_stopping": false,
            "seed": -1,
            "add_bos_token": true,
            "truncation_length": 2048,
            "ban_eos_token": false,
            "skip_special_tokens": true,
            "stopping_strings": []
        });

        let response = self.client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow!("Text Generation WebUI API error: {}", error_text));
        }

        let response_json: Value = response.json().await?;

        let content = response_json
            .get("results")
            .and_then(|r| r.get(0))
            .and_then(|r| r.get("text"))
            .and_then(|t| t.as_str())
            .ok_or_else(|| anyhow!("Invalid response format from Text Generation WebUI API"))?;

        Ok(content.to_string())
    }

    // KoboldCpp API
    async fn call_kobold_api(&self, prompt: &str, config: &AIProviderConfig) -> Result<String> {
        let url = format!("{}/api/v1/generate", config.base_url.as_ref().unwrap());

        let payload = json!({
            "prompt": prompt,
            "max_length": 4096,
            "max_context_length": 2048,
            "temperature": 0.7,
            "top_p": 0.9,
            "top_k": 40,
            "rep_pen": 1.1,
            "rep_pen_range": 1024,
            "rep_pen_slope": 0.9,
            "tfs": 1.0,
            "typical": 1.0,
            "sampler_order": [6, 0, 1, 3, 4, 2, 5],
            "use_default_badwordsids": false
        });

        let response = self.client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow!("KoboldCpp API error: {}", error_text));
        }

        let response_json: Value = response.json().await?;

        let content = response_json
            .get("results")
            .and_then(|r| r.get(0))
            .and_then(|r| r.get("text"))
            .and_then(|t| t.as_str())
            .ok_or_else(|| anyhow!("Invalid response format from KoboldCpp API"))?;

        Ok(content.to_string())
    }
}
