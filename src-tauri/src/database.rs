use sqlx::{sqlite::SqlitePool, Row};
use anyhow::Result;
use uuid::Uuid;
use chrono::Utc;
use crate::models::*;

pub struct Database {
    pool: SqlitePool,
}

impl Database {
    pub async fn new(database_url: &str) -> Result<Self> {
        let pool = SqlitePool::connect(database_url).await?;

        // Create tables if they don't exist
        sqlx::query(include_str!("../migrations/001_initial.sql"))
            .execute(&pool)
            .await?;

        Ok(Database { pool })
    }

    pub async fn create_project(&self, name: &str, goal: &str) -> Result<AIProject> {
        let id = Uuid::new_v4();
        let now = Utc::now();

        let project = AIProject {
            id,
            name: name.to_string(),
            goal: goal.to_string(),
            tasks: Vec::new(),
            files: Vec::new(),
            status: AIProjectStatus::Planning,
            current_task_index: -1,
            overall_progress: 0,
            creation_date: now,
            last_updated_date: now,
            error_log: Vec::new(),
            chat_history: Some(vec![ConversationTurn {
                id: Uuid::new_v4(),
                role: "ai".to_string(),
                content: format!("Project \"{}\" created with goal: \"{}\". Starting task planning.", name, goal),
                timestamp: now,
                related_file: None,
            }]),
        };

        sqlx::query(
            r#"
            INSERT INTO projects (id, name, goal, status, current_task_index, overall_progress, creation_date, last_updated_date)
            VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8)
            "#
        )
        .bind(project.id.to_string())
        .bind(&project.name)
        .bind(&project.goal)
        .bind(serde_json::to_string(&project.status)?)
        .bind(project.current_task_index)
        .bind(project.overall_progress)
        .bind(project.creation_date)
        .bind(project.last_updated_date)
        .execute(&self.pool)
        .await?;

        Ok(project)
    }

    pub async fn get_all_projects(&self) -> Result<Vec<AIProject>> {
        let rows = sqlx::query(
            "SELECT id, name, goal, status, current_task_index, overall_progress, creation_date, last_updated_date FROM projects ORDER BY last_updated_date DESC"
        )
        .fetch_all(&self.pool)
        .await?;

        let mut projects = Vec::new();
        for row in rows {
            let id = Uuid::parse_str(row.get("id"))?;
            let status: AIProjectStatus = serde_json::from_str(row.get("status"))?;

            // Load tasks, files, and chat history
            let tasks = self.get_project_tasks(id).await?;
            let files = self.get_project_files(id).await?;
            let chat_history = self.get_project_chat_history(id).await?;
            let error_log = self.get_project_error_log(id).await?;

            projects.push(AIProject {
                id,
                name: row.get("name"),
                goal: row.get("goal"),
                tasks,
                files,
                status,
                current_task_index: row.get("current_task_index"),
                overall_progress: row.get("overall_progress"),
                creation_date: row.get("creation_date"),
                last_updated_date: row.get("last_updated_date"),
                error_log,
                chat_history: Some(chat_history),
            });
        }

        Ok(projects)
    }

    pub async fn get_project(&self, project_id: Uuid) -> Result<Option<AIProject>> {
        let row = sqlx::query(
            "SELECT id, name, goal, status, current_task_index, overall_progress, creation_date, last_updated_date FROM projects WHERE id = ?1"
        )
        .bind(project_id.to_string())
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = row {
            let status: AIProjectStatus = serde_json::from_str(row.get("status"))?;

            let tasks = self.get_project_tasks(project_id).await?;
            let files = self.get_project_files(project_id).await?;
            let chat_history = self.get_project_chat_history(project_id).await?;
            let error_log = self.get_project_error_log(project_id).await?;

            Ok(Some(AIProject {
                id: project_id,
                name: row.get("name"),
                goal: row.get("goal"),
                tasks,
                files,
                status,
                current_task_index: row.get("current_task_index"),
                overall_progress: row.get("overall_progress"),
                creation_date: row.get("creation_date"),
                last_updated_date: row.get("last_updated_date"),
                error_log,
                chat_history: Some(chat_history),
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn update_project(&self, project: &AIProject) -> Result<()> {
        sqlx::query(
            r#"
            UPDATE projects
            SET name = ?2, goal = ?3, status = ?4, current_task_index = ?5, overall_progress = ?6, last_updated_date = ?7
            WHERE id = ?1
            "#
        )
        .bind(project.id.to_string())
        .bind(&project.name)
        .bind(serde_json::to_string(&project.status)?)
        .bind(project.current_task_index)
        .bind(project.overall_progress)
        .bind(project.last_updated_date)
        .execute(&self.pool)
        .await?;

        // Update tasks, files, chat history, and error log
        self.update_project_tasks(project.id, &project.tasks).await?;
        self.update_project_files(project.id, &project.files).await?;
        if let Some(chat_history) = &project.chat_history {
            self.update_project_chat_history(project.id, chat_history).await?;
        }
        self.update_project_error_log(project.id, &project.error_log).await?;

        Ok(())
    }

    async fn get_project_tasks(&self, _project_id: Uuid) -> Result<Vec<AITask>> {
        // Simplified for now - return empty tasks
        Ok(Vec::new())
    }

    async fn get_task_logs(&self, _task_id: Uuid) -> Result<Vec<String>> {
        // Simplified for now
        Ok(Vec::new())
    }

    async fn get_project_files(&self, _project_id: Uuid) -> Result<Vec<AppFile>> {
        // Simplified for now
        Ok(Vec::new())
    }

    async fn get_project_chat_history(&self, _project_id: Uuid) -> Result<Vec<ConversationTurn>> {
        // Simplified for now
        Ok(Vec::new())
    }

    async fn get_project_error_log(&self, _project_id: Uuid) -> Result<Vec<String>> {
        // Simplified for now
        Ok(Vec::new())
    }

    // Update methods would be implemented here...
    async fn update_project_tasks(&self, _project_id: Uuid, _tasks: &[AITask]) -> Result<()> {
        // Implementation for updating tasks
        Ok(())
    }

    async fn update_project_files(&self, _project_id: Uuid, _files: &[AppFile]) -> Result<()> {
        // Implementation for updating files
        Ok(())
    }

    async fn update_project_chat_history(&self, _project_id: Uuid, _chat_history: &[ConversationTurn]) -> Result<()> {
        // Implementation for updating chat history
        Ok(())
    }

    async fn update_project_error_log(&self, _project_id: Uuid, _error_log: &[String]) -> Result<()> {
        // Implementation for updating error log
        Ok(())
    }
}
