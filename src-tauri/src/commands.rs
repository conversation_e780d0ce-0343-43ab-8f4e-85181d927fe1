use tauri::State;
use uuid::Uuid;
use anyhow::Result;
use crate::models::*;
use crate::ai_service::parse_ai_response_to_files;
use crate::AppState;

#[tauri::command]
pub async fn get_all_projects(state: State<'_, AppState>) -> Result<Vec<AIProject>, String> {
    state.database.get_all_projects()
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_project(project_id: String, state: State<'_, AppState>) -> Result<Option<AIProject>, String> {
    let uuid = Uuid::parse_str(&project_id).map_err(|e| e.to_string())?;
    state.database.get_project(uuid)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn create_project(
    request: CreateProjectRequest,
    state: State<'_, AppState>,
) -> Result<AIProject, String> {
    // Create the project in database
    let mut project = state.database.create_project(&request.name, &request.goal)
        .await
        .map_err(|e| e.to_string())?;

    // Plan tasks using AI
    match state.ai_service.plan_project_tasks(&request.goal).await {
        Ok(task_descriptions) => {
            let tasks: Vec<AITask> = task_descriptions
                .into_iter()
                .map(|desc| AITask {
                    id: Uuid::new_v4(),
                    description: desc,
                    status: AITaskStatus::Pending,
                    logs: Vec::new(),
                    generated_content_preview: None,
                    attempt_count: 0,
                })
                .collect();

            project.tasks = tasks;
            project.status = AIProjectStatus::ReadyToDevelop;
            project.last_updated_date = chrono::Utc::now();

            // Update project in database
            state.database.update_project(&project)
                .await
                .map_err(|e| e.to_string())?;

            Ok(project)
        }
        Err(e) => {
            project.status = AIProjectStatus::Error;
            project.error_log.push(format!("Failed to plan tasks: {}", e));
            project.last_updated_date = chrono::Utc::now();

            state.database.update_project(&project)
                .await
                .map_err(|e| e.to_string())?;

            Err(format!("Failed to plan tasks: {}", e))
        }
    }
}

#[tauri::command]
pub async fn start_ai_development(
    project_id: String,
    state: State<'_, AppState>,
) -> Result<AIProject, String> {
    let uuid = Uuid::parse_str(&project_id).map_err(|e| e.to_string())?;
    let mut project = state.database.get_project(uuid)
        .await
        .map_err(|e| e.to_string())?
        .ok_or("Project not found")?;

    if !matches!(project.status, AIProjectStatus::ReadyToDevelop | AIProjectStatus::Paused) {
        return Err("Project is not ready for development".to_string());
    }

    project.status = AIProjectStatus::Developing;
    project.last_updated_date = chrono::Utc::now();

    let start_index = if project.current_task_index == -1 { 0 } else { project.current_task_index as usize };

    for i in start_index..project.tasks.len() {
        if matches!(project.tasks[i].status, AITaskStatus::Completed | AITaskStatus::AwaitingReview) {
            continue;
        }

        project.current_task_index = i as i32;
        project.tasks[i].status = AITaskStatus::InProgress;

        // Execute the task
        match state.ai_service.execute_development_task(
            &project.tasks[i].description,
            &project.goal,
            &project.files,
            project.chat_history.as_deref(),
        ).await {
            Ok(response) => {
                let new_files = parse_ai_response_to_files(&response);

                // Update project files
                for new_file in new_files {
                    if let Some(existing_file) = project.files.iter_mut().find(|f| f.name == new_file.name) {
                        *existing_file = new_file;
                    } else {
                        project.files.push(new_file);
                    }
                }

                project.tasks[i].status = AITaskStatus::Completed;
                project.tasks[i].generated_content_preview = Some(
                    format!("{} files affected", project.files.len())
                );
                project.overall_progress = ((i + 1) * 100 / project.tasks.len()) as i32;
            }
            Err(e) => {
                project.tasks[i].status = AITaskStatus::Error;
                project.tasks[i].logs.push(format!("Error: {}", e));
                project.status = AIProjectStatus::Error;
                project.error_log.push(format!("Task '{}' failed: {}", project.tasks[i].description, e));
                break;
            }
        }

        project.last_updated_date = chrono::Utc::now();
        state.database.update_project(&project)
            .await
            .map_err(|e| e.to_string())?;
    }

    // If all tasks completed successfully
    if project.status == AIProjectStatus::Developing {
        project.status = AIProjectStatus::ReviewPending;
        project.current_task_index = (project.tasks.len() - 1) as i32;
    }

    project.last_updated_date = chrono::Utc::now();
    state.database.update_project(&project)
        .await
        .map_err(|e| e.to_string())?;

    Ok(project)
}

#[tauri::command]
pub async fn update_file_content(
    request: UpdateFileRequest,
    state: State<'_, AppState>,
) -> Result<AIProject, String> {
    let mut project = state.database.get_project(request.project_id)
        .await
        .map_err(|e| e.to_string())?
        .ok_or("Project not found")?;

    if let Some(file) = project.files.iter_mut().find(|f| f.name == request.file_path) {
        file.content = request.content;
        file.last_modified = Some(chrono::Utc::now());
    } else {
        project.files.push(AppFile {
            name: request.file_path.clone(),
            content: request.content,
            language: None,
            last_modified: Some(chrono::Utc::now()),
        });
    }

    project.last_updated_date = chrono::Utc::now();
    state.database.update_project(&project)
        .await
        .map_err(|e| e.to_string())?;

    Ok(project)
}

#[tauri::command]
pub async fn start_refinement(
    request: StartRefinementRequest,
    state: State<'_, AppState>,
) -> Result<AIProject, String> {
    let mut project = state.database.get_project(request.project_id)
        .await
        .map_err(|e| e.to_string())?
        .ok_or("Project not found")?;

    project.status = AIProjectStatus::Refining;

    // Add user message to chat history
    let user_turn = ConversationTurn {
        id: Uuid::new_v4(),
        role: "user".to_string(),
        content: request.prompt.clone(),
        timestamp: chrono::Utc::now(),
        related_file: None,
    };

    if let Some(ref mut chat_history) = project.chat_history {
        chat_history.push(user_turn);
    } else {
        project.chat_history = Some(vec![user_turn]);
    }

    // Execute refinement
    match state.ai_service.execute_refinement_task(
        &request.prompt,
        &project.goal,
        &project.files,
        project.chat_history.as_deref(),
    ).await {
        Ok(response) => {
            let new_files = parse_ai_response_to_files(&response);
            let affected_files: Vec<String> = new_files.iter().map(|f| f.name.clone()).collect();

            // Update project files
            for new_file in new_files {
                if let Some(existing_file) = project.files.iter_mut().find(|f| f.name == new_file.name) {
                    *existing_file = new_file;
                } else {
                    project.files.push(new_file);
                }
            }

            // Add AI response to chat history
            let ai_turn = ConversationTurn {
                id: Uuid::new_v4(),
                role: "ai".to_string(),
                content: format!("Refinement applied. Files affected: {}",
                    if affected_files.is_empty() { "None".to_string() } else { affected_files.join(", ") }),
                timestamp: chrono::Utc::now(),
                related_file: None,
            };

            if let Some(ref mut chat_history) = project.chat_history {
                chat_history.push(ai_turn);
            }

            project.status = AIProjectStatus::Completed;
        }
        Err(e) => {
            project.status = AIProjectStatus::Error;
            project.error_log.push(format!("Refinement failed: {}", e));

            // Add error to chat history
            let ai_turn = ConversationTurn {
                id: Uuid::new_v4(),
                role: "ai".to_string(),
                content: format!("Error during refinement: {}", e),
                timestamp: chrono::Utc::now(),
                related_file: None,
            };

            if let Some(ref mut chat_history) = project.chat_history {
                chat_history.push(ai_turn);
            }
        }
    }

    project.last_updated_date = chrono::Utc::now();
    state.database.update_project(&project)
        .await
        .map_err(|e| e.to_string())?;

    Ok(project)
}

#[tauri::command]
pub async fn confirm_review(
    request: ReviewConfirmationRequest,
    state: State<'_, AppState>,
) -> Result<AIProject, String> {
    let mut project = state.database.get_project(request.project_id)
        .await
        .map_err(|e| e.to_string())?
        .ok_or("Project not found")?;

    if request.confirm {
        project.status = AIProjectStatus::Completed;
    } else {
        project.status = AIProjectStatus::Error;
        if let Some(feedback) = request.feedback {
            project.error_log.push(format!("User rejected review. Feedback: {}", feedback));
        } else {
            project.error_log.push("User rejected review. No feedback provided.".to_string());
        }
    }

    project.last_updated_date = chrono::Utc::now();
    state.database.update_project(&project)
        .await
        .map_err(|e| e.to_string())?;

    Ok(project)
}

// AI Provider Management Commands
#[tauri::command]
pub async fn get_available_models(
    provider: Option<AIProvider>,
    state: State<'_, AppState>,
) -> Result<Vec<AIModel>, String> {
    Ok(state.ai_service.get_available_models(provider))
}

#[tauri::command]
pub async fn get_enabled_providers(state: State<'_, AppState>) -> Result<Vec<AIProviderConfig>, String> {
    Ok(state.ai_service.get_enabled_providers())
}