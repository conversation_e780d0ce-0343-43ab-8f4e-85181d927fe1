# 🤖 多 AI 供应商支持指南

## 🎉 功能概述

您的 Autonomous AI IDE 现在支持**10 种不同的 AI 供应商**，包括**5 种完全免费的本地解决方案**！

## 🏠 本地 AI 模型 (100% 免费)

### 1. **🦙 Ollama (推荐新手)**
- **Llama 3.2 3B** - 通用编程模型
- **Code Llama 7B** - 专业代码生成
- **DeepSeek Coder 6.7B** - 优秀的代码理解
- **Qwen 2.5 3B** - 中文友好模型
- **安装**: `curl -fsSL https://ollama.ai/install.sh | sh`
- **优势**: 一键安装，自动管理，简单易用

### 2. **🖥️ LM Studio (推荐 GUI 用户)**
- **本地模型** - 图形界面管理
- **多种格式支持** - GGUF, GGML 等
- **性能优化** - 自动硬件检测
- **下载**: https://lmstudio.ai/
- **优势**: 用户友好，性能优秀，模型管理简单

### 3. **🔧 Text Generation WebUI (高级用户)**
- **功能最丰富** - 支持各种模型格式
- **高度可定制** - 丰富的参数调节
- **社区活跃** - 插件生态丰富
- **安装**: https://github.com/oobabooga/text-generation-webui
- **优势**: 功能强大，扩展性好

### 4. **⚡ KoboldCpp (轻量级)**
- **高性能** - C++ 实现，速度快
- **轻量级** - 资源占用少
- **多平台** - 支持各种操作系统
- **下载**: https://github.com/LostRuins/koboldcpp
- **优势**: 性能优秀，资源效率高

### 5. **🔥 llama.cpp Server (极致性能)**
- **极致性能** - 原生 C++ 实现
- **内存效率** - 量化支持，内存占用低
- **硬件加速** - 支持 GPU、Metal 等
- **安装**: https://github.com/ggerganov/llama.cpp
- **优势**: 最高性能，最低资源消耗

### 6. **⚙️ 自定义本地 API**
- **完全自定义** - 支持任何兼容 API
- **灵活配置** - 自定义端点和参数
- **环境变量配置** - 简单设置
- **优势**: 最大灵活性，适配任何本地服务

## 🌐 云端 AI 模型

### 7. **OpenRouter (推荐免费云端)**
- **Llama 3.2 3B** - Meta 的开源模型，免费使用
- **Llama 3.2 1B** - 轻量级版本，速度更快
- **Phi-3 Mini** - Microsoft 的高效小模型
- **Gemma 2 9B** - Google 的开源模型
- **获取 API 密钥**: https://openrouter.ai/keys
- **免费额度**: 每月大量免费请求

### 8. **Google Gemini**
- **Gemini 1.5 Flash** - 快速响应，适合开发任务
- **Gemini 1.5 Pro** - 更强大的推理能力
- **获取 API 密钥**: https://makersuite.google.com/app/apikey
- **免费额度**: 每月 15 次免费请求

## 💰 付费 AI 模型

### 9. **OpenAI**
- **GPT-3.5 Turbo** - 经典的 ChatGPT 模型
- **GPT-4o Mini** - 最新的高效模型
- **获取 API 密钥**: https://platform.openai.com/api-keys

### 10. **Anthropic Claude**
- **Claude 3.5 Sonnet** - 优秀的代码生成能力
- **获取 API 密钥**: https://console.anthropic.com/

## 🔧 配置方法

### 方法 1: 本地 AI (推荐，100% 免费)

```bash
# 选项 A: Ollama (最简单)
curl -fsSL https://ollama.ai/install.sh | sh
ollama pull llama3.2:3b
ollama pull deepseek-coder:6.7b

# 选项 B: LM Studio (GUI 用户)
# 1. 下载并安装 LM Studio: https://lmstudio.ai/
# 2. 在应用中下载模型
# 3. 启动本地服务器

# 选项 C: 自定义本地 API
export CUSTOM_BASE_URL="http://localhost:8000"
export CUSTOM_MODEL_ID="your-model-name"

# 启动应用 (无需 API 密钥)
npm run tauri:dev
```

### 方法 2: 云端免费 API

```bash
# 免费云端选项
export OPENROUTER_API_KEY="sk-or-v1-your-key-here"  # 免费额度
export GEMINI_API_KEY="your-gemini-key-here"        # 免费额度

# 启动应用
npm run tauri:dev
```

### 方法 3: 付费 API (可选)

```bash
# 付费选项 (更高性能)
export OPENAI_API_KEY="sk-your-openai-key-here"
export ANTHROPIC_API_KEY="sk-ant-your-anthropic-key-here"

# 启动应用
npm run tauri:dev
```

### 方法 4: .env 文件配置

```bash
# 复制配置模板
cp .env.example .env

# 编辑 .env 文件，取消注释需要的配置
# 本地 AI 无需配置，云端 API 需要填入密钥
```

## 🎯 推荐配置

### 🆓 完全免费方案 (推荐)
```bash
# 本地 AI - 无限制，完全私密
curl -fsSL https://ollama.ai/install.sh | sh
ollama pull llama3.2:3b          # 通用编程
ollama pull deepseek-coder:6.7b  # 专业代码

# 可选：云端备份
export OPENROUTER_API_KEY="your-free-key"  # 免费额度
export GEMINI_API_KEY="your-free-key"      # 免费额度
```

### 💡 混合方案 (本地 + 云端)
```bash
# 主力：本地 AI (无限制)
ollama pull llama3.2:3b

# 备用：云端 API (复杂任务)
export OPENROUTER_API_KEY="your-key"
export GEMINI_API_KEY="your-key"
```

### 🚀 专业开发方案
```bash
# 本地高性能模型
ollama pull deepseek-coder:6.7b
ollama pull codellama:7b

# 全套云端 API
export OPENROUTER_API_KEY="your-key"
export GEMINI_API_KEY="your-key"
export OPENAI_API_KEY="your-key"
export ANTHROPIC_API_KEY="your-key"

# 自定义本地服务
export CUSTOM_BASE_URL="http://localhost:8000"
```

## 🔍 查看可用模型

在应用中点击 **"AI Settings"** 按钮查看：
- 所有可用的 AI 模型
- 每个供应商的状态
- 免费模型列表
- 配置说明

## 🔄 模型自动选择

应用会自动选择第一个可用的 AI 供应商：
1. **优先级**: Gemini → OpenRouter → OpenAI → Anthropic → Ollama
2. **自动切换**: 如果当前模型不可用，自动尝试下一个
3. **智能选择**: 根据任务类型选择最适合的模型

## 📊 模型对比

| 供应商 | 模型 | 免费额度 | 响应速度 | 代码质量 | 隐私性 | 推荐用途 |
|--------|------|----------|----------|----------|---------|----------|
| **本地 AI** |
| Ollama | Llama 3.2 3B | ✅ 无限 | ⚡ 快 | 🟢 好 | 🔒 完全 | 日常开发 |
| LM Studio | 本地模型 | ✅ 无限 | ⚡ 很快 | 🟢 很好 | 🔒 完全 | GUI 用户 |
| Text Gen WebUI | 自选模型 | ✅ 无限 | ⚡ 快 | 🟢 优秀 | 🔒 完全 | 高级用户 |
| KoboldCpp | 本地模型 | ✅ 无限 | ⚡ 很快 | 🟢 好 | 🔒 完全 | 轻量级 |
| llama.cpp | 本地模型 | ✅ 无限 | ⚡ 极快 | 🟢 好 | 🔒 完全 | 极致性能 |
| **云端 AI** |
| OpenRouter | Llama 3.2 3B | ✅ 大量 | ⚡ 快 | 🟢 好 | ⚠️ 云端 | 免费云端 |
| Gemini | 1.5 Flash | ✅ 15次/月 | ⚡ 很快 | 🟢 很好 | ⚠️ 云端 | 快速原型 |
| OpenAI | GPT-4o Mini | ❌ 付费 | ⚡ 快 | 🟢 优秀 | ⚠️ 云端 | 专业开发 |
| Anthropic | Claude 3.5 | ❌ 付费 | ⚡ 快 | 🟢 优秀 | ⚠️ 云端 | 复杂任务 |

## 🛠️ 故障排除

### 问题: "Failed to create project"
**解决方案**:
1. 检查是否设置了至少一个 API 密钥
2. 验证 API 密钥是否有效
3. 检查网络连接

### 问题: "No API key found"
**解决方案**:
1. 使用本地 AI (推荐): 安装 Ollama 或 LM Studio
2. 或设置云端 API 密钥后重启应用

### 问题: 本地模型连接失败
**解决方案**:
```bash
# Ollama
ollama serve
ollama pull llama3.2:3b

# LM Studio
# 在 LM Studio 中启动本地服务器

# 检查端口
lsof -i :11434  # Ollama
lsof -i :1234   # LM Studio
```

### 问题: 模型响应慢
**解决方案**:
1. 使用更小的模型 (3B 而不是 7B)
2. 启用 GPU 加速
3. 增加系统内存
4. 使用量化模型

## 🎯 最佳实践

1. **首选本地 AI**: 无限制、私密、免费
2. **云端作备用**: 处理复杂任务时使用
3. **模型选择**: 根据硬件配置选择合适大小
4. **混合策略**: 本地 + 云端，获得最佳体验

## 🚀 快速开始

### 🏠 本地 AI (5 分钟设置)
```bash
# 最简单的免费方案
curl -fsSL https://ollama.ai/install.sh | sh
ollama pull llama3.2:3b
npm run tauri:dev
```

### 🌐 云端 AI (2 分钟设置)
```bash
# 注册免费账户并获取 API 密钥
export OPENROUTER_API_KEY="your-free-key"
npm run tauri:dev
```

### 🔥 混合方案 (最佳体验)
```bash
# 本地 + 云端
ollama pull deepseek-coder:6.7b
export OPENROUTER_API_KEY="your-free-key"
npm run tauri:dev
```

## 🔮 未来计划

- [ ] 模型性能监控和统计
- [ ] 自定义模型参数调节
- [ ] 成本跟踪和使用分析
- [ ] 智能模型切换策略
- [ ] 更多本地 AI 解决方案支持
- [ ] 模型微调和训练集成

---

**🎊 恭喜！您现在拥有一个支持 10 种 AI 供应商的强大 IDE！**

**✨ 特色功能:**
- 🏠 **5 种本地 AI** - 完全免费，无限制，保护隐私
- 🌐 **5 种云端 AI** - 包含免费和付费选项
- 🔄 **智能切换** - 自动选择最佳可用模型
- ⚙️ **灵活配置** - 支持自定义 API 端点
- 🎯 **专业优化** - 针对编程任务优化的模型选择

**立即开始您的 AI 编程之旅！** 🚀
