# 🤖 多 AI 供应商支持指南

## 🎉 功能概述

您的 Autonomous AI IDE 现在支持多个 AI 供应商，包括免费和付费选项！

## 🆓 支持的免费 AI 模型

### 1. **OpenRouter (推荐免费选项)**
- **Llama 3.2 3B** - Meta 的开源模型，免费使用
- **Llama 3.2 1B** - 轻量级版本，速度更快
- **Phi-3 Mini** - Microsoft 的高效小模型
- **Gemma 2 9B** - Google 的开源模型
- **获取 API 密钥**: https://openrouter.ai/keys
- **免费额度**: 每月大量免费请求

### 2. **Google Gemini**
- **Gemini 1.5 Flash** - 快速响应，适合开发任务
- **Gemini 1.5 Pro** - 更强大的推理能力
- **获取 API 密钥**: https://makersuite.google.com/app/apikey
- **免费额度**: 每月 15 次免费请求

### 3. **Ollama (本地运行)**
- **Llama 3.2 3B** - 本地运行，完全免费
- **Qwen 2.5 3B** - 阿里巴巴的开源模型
- **安装**: https://ollama.ai/
- **优势**: 无需 API 密钥，数据完全本地化

## 💰 支持的付费 AI 模型

### 4. **OpenAI**
- **GPT-3.5 Turbo** - 经典的 ChatGPT 模型
- **GPT-4o Mini** - 最新的高效模型
- **获取 API 密钥**: https://platform.openai.com/api-keys

### 5. **Anthropic Claude**
- **Claude 3.5 Sonnet** - 优秀的代码生成能力
- **获取 API 密钥**: https://console.anthropic.com/

## 🔧 配置方法

### 方法 1: 环境变量 (推荐)

```bash
# 免费选项 (选择一个或多个)
export OPENROUTER_API_KEY="sk-or-v1-your-key-here"
export GEMINI_API_KEY="your-gemini-key-here"

# 付费选项 (可选)
export OPENAI_API_KEY="sk-your-openai-key-here"
export ANTHROPIC_API_KEY="sk-ant-your-anthropic-key-here"

# 启动应用
npm run tauri:dev
```

### 方法 2: .env 文件

创建 `.env` 文件：

```env
# 复制 .env.example 并填入您的密钥
cp .env.example .env

# 编辑 .env 文件
OPENROUTER_API_KEY=sk-or-v1-your-key-here
GEMINI_API_KEY=your-gemini-key-here
```

### 方法 3: Ollama 本地模型

```bash
# 安装 Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 下载模型
ollama pull llama3.2:3b
ollama pull qwen2.5:3b

# 启动应用 (无需 API 密钥)
npm run tauri:dev
```

## 🎯 推荐配置

### 🆓 完全免费方案
```bash
# 1. 安装 Ollama (本地模型)
ollama pull llama3.2:3b

# 2. 注册 OpenRouter 免费账户
export OPENROUTER_API_KEY="your-free-key"

# 3. 注册 Gemini 免费账户
export GEMINI_API_KEY="your-free-key"
```

### 💡 最佳性价比方案
```bash
# OpenRouter (免费额度) + Gemini (免费额度)
export OPENROUTER_API_KEY="your-key"
export GEMINI_API_KEY="your-key"
```

### 🚀 专业开发方案
```bash
# 全套 AI 供应商
export OPENROUTER_API_KEY="your-key"
export GEMINI_API_KEY="your-key"
export OPENAI_API_KEY="your-key"
export ANTHROPIC_API_KEY="your-key"
```

## 🔍 查看可用模型

在应用中点击 **"AI Settings"** 按钮查看：
- 所有可用的 AI 模型
- 每个供应商的状态
- 免费模型列表
- 配置说明

## 🔄 模型自动选择

应用会自动选择第一个可用的 AI 供应商：
1. **优先级**: Gemini → OpenRouter → OpenAI → Anthropic → Ollama
2. **自动切换**: 如果当前模型不可用，自动尝试下一个
3. **智能选择**: 根据任务类型选择最适合的模型

## 📊 模型对比

| 供应商 | 模型 | 免费额度 | 响应速度 | 代码质量 | 推荐用途 |
|--------|------|----------|----------|----------|----------|
| OpenRouter | Llama 3.2 3B | ✅ 大量 | ⚡ 快 | 🟢 好 | 日常开发 |
| Gemini | 1.5 Flash | ✅ 15次/月 | ⚡ 很快 | 🟢 很好 | 快速原型 |
| Ollama | Llama 3.2 3B | ✅ 无限 | 🐌 中等 | 🟢 好 | 隐私项目 |
| OpenAI | GPT-4o Mini | ❌ 付费 | ⚡ 快 | 🟢 优秀 | 专业开发 |
| Anthropic | Claude 3.5 | ❌ 付费 | ⚡ 快 | 🟢 优秀 | 复杂任务 |

## 🛠️ 故障排除

### 问题: "Failed to create project"
**解决方案**: 
1. 检查是否设置了至少一个 API 密钥
2. 验证 API 密钥是否有效
3. 检查网络连接

### 问题: "No API key found"
**解决方案**:
1. 设置环境变量后重启应用
2. 或者安装 Ollama 使用本地模型

### 问题: Ollama 连接失败
**解决方案**:
1. 确保 Ollama 服务正在运行: `ollama serve`
2. 下载所需模型: `ollama pull llama3.2:3b`

## 🎯 最佳实践

1. **开发阶段**: 使用免费的 OpenRouter 或 Gemini
2. **生产环境**: 考虑付费模型获得更好的性能
3. **隐私敏感**: 使用 Ollama 本地模型
4. **混合使用**: 配置多个供应商作为备选

## 🔮 未来计划

- [ ] 模型性能监控
- [ ] 自定义模型参数
- [ ] 成本跟踪
- [ ] 模型切换策略
- [ ] 更多免费模型支持

---

**🎊 恭喜！您现在拥有一个支持多个 AI 供应商的强大 IDE，可以根据需求选择最适合的模型！**
