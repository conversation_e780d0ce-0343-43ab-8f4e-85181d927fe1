# AI Provider API Keys
# Configure one or more providers - the app will use the first available one

# Google Gemini (Free tier available)
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your-gemini-api-key-here

# OpenRouter (Free tier with multiple models)
# Get your API key from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your-openrouter-api-key-here

# OpenAI (Paid service)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your-openai-api-key-here

# Anthropic Claude (Paid service)
# Get your API key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Ollama (Local models - no API key needed)
# Install Ollama from: https://ollama.ai/
# No API key required, runs locally

# Legacy support (for backward compatibility)
API_KEY=your-gemini-api-key-here

# Tauri Environment
TAURI_DEBUG=true

# Recommended Free Options:
# 1. OpenRouter - Free tier with Llama 3.2, Phi-3, Gemma models
# 2. Gemini - Generous free tier from Google
# 3. Ollama - Run models locally (no API key needed)
