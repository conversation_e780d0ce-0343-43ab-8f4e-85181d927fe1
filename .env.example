# AI Provider API Keys
# Configure one or more providers - the app will use the first available one

# Google Gemini (Free tier available)
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your-gemini-api-key-here

# OpenRouter (Free tier with multiple models)
# Get your API key from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your-openrouter-api-key-here

# OpenAI (Paid service)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your-openai-api-key-here

# Anthropic Claude (Paid service)
# Get your API key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Local AI Solutions (100% Free - No API Keys Needed)

# Ollama - Easy local AI with one-click downloads
# Install: https://ollama.ai/
# Usage: ollama pull llama3.2:3b
# Default port: 11434

# LM Studio - User-friendly GUI for local models
# Install: https://lmstudio.ai/
# Default port: 1234

# Text Generation WebUI (oobabooga) - Advanced local AI
# Install: https://github.com/oobabooga/text-generation-webui
# Default port: 5000

# KoboldCpp - Lightweight local inference
# Install: https://github.com/LostRuins/koboldcpp
# Default port: 5001

# llama.cpp Server - High-performance C++ implementation
# Install: https://github.com/ggerganov/llama.cpp
# Default port: 8080

# Custom Local API - Configure your own endpoint
# CUSTOM_BASE_URL=http://localhost:8000
# CUSTOM_API_KEY=optional-api-key
# CUSTOM_MODEL_ID=your-model-name

# Legacy support (for backward compatibility)
API_KEY=your-gemini-api-key-here

# Tauri Environment
TAURI_DEBUG=true

# Recommended Setup Priority:
# 1. Local AI (Ollama/LM Studio) - 100% free, private, no limits
# 2. OpenRouter - Free tier with multiple models
# 3. Gemini - Google's free tier
# 4. Paid services for production use
