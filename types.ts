export interface AppFile {
  name: string; // Full path, e.g., src/components/Button.tsx
  content: string;
  language?: string;
  lastModified?: string; // ISO string, for tracking user edits
}

export type AITaskStatus = 'pending' | 'in-progress' | 'completed' | 'error' | 'awaiting-review';

export interface AITask {
  id: string;
  description: string;
  status: AITaskStatus;
  logs: string[]; // Log messages for this task
  generatedContentPreview?: string; // A snippet or summary of what was generated
  attemptCount: number;
}

export type AIProjectStatus =
  'new' |
  'planning' |
  'ready-to-develop' |
  'developing' |
  'paused' |
  'completed' |
  'error' |
  'review-pending' |
  'refining'; // New status for post-completion modifications

export interface ConversationTurn {
  id: string;
  role: 'user' | 'ai';
  content: string;
  timestamp: string;
  relatedFile?: string; // Optional: link to a file discussed
}

export interface AIProject {
  id: string;
  name: string;
  goal: string;
  tasks: AITask[];
  files: AppFile[];
  status: AIProjectStatus;
  currentTaskIndex: number; // Index of the task currently being processed or last processed
  overallProgress: number; // Percentage (0-100)
  creationDate: string;
  lastUpdatedDate: string;
  errorLog: string[]; // Project-level errors
  chatHistory?: ConversationTurn[]; // For refinement conversations
}

// For FileExplorer
export interface FileSystemNode {
  id: string; // Typically the full path
  name: string; // The file or folder name
  path: string; // Full path
  type: 'file' | 'folder';
  children?: FileSystemNode[];
  // For files, directly include AppFile properties or reference it
  content?: string;
  language?: string;
  lastModified?: string;
}

// Tauri Request/Response types
export interface CreateProjectRequest {
  name: string;
  goal: string;
}

export interface UpdateFileRequest {
  project_id: string;
  file_path: string;
  content: string;
}

export interface StartRefinementRequest {
  project_id: string;
  prompt: string;
}

export interface ReviewConfirmationRequest {
  project_id: string;
  confirm: boolean;
  feedback?: string;
}

// AI Provider Types
export type AIProvider = 'gemini' | 'openrouter' | 'openai' | 'anthropic' | 'ollama' | 'lmstudio' | 'textgen' | 'koboldcpp' | 'llamacpp' | 'custom';

export interface AIModel {
  id: string;
  name: string;
  provider: AIProvider;
  max_tokens?: number;
  cost_per_token?: number;
  supports_streaming: boolean;
  context_window?: number;
}

export interface AIProviderConfig {
  provider: AIProvider;
  api_key?: string;
  base_url?: string;
  model_id: string;
  enabled: boolean;
}