import React, { useEffect, useRef } from 'react';
import { AIProject, AITask } from '../types';
import { Bot, TerminalSquare } from 'lucide-react';

interface AiLogPaneProps {
  project: AIProject | null;
}

export const AiLogPane: React.FC<AiLogPaneProps> = ({ project }) => {
  const logsEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    logsEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [project?.tasks, project?.errorLog]); // Scroll on new logs

  if (!project) {
    return (
      <aside className="w-80 bg-surface border-l border-gray-700 p-3 flex flex-col shrink-0">
        <h3 className="text-base font-semibold text-on-surface mb-3 flex items-center">
          <Bot size={18} className="mr-2 text-primary/80" />
          AI Assistant Log
        </h3>
        <p className="text-sm text-on-surface-muted">No active project.</p>
      </aside>
    );
  }

  const currentTask = project.currentTaskIndex >= 0 && project.currentTaskIndex < project.tasks.length 
                      ? project.tasks[project.currentTaskIndex] 
                      : null;
  
  let allLogs: {timestamp?: string, message: string, type: 'task' | 'project' | 'system'}[] = [];

  project.tasks.forEach(task => {
    task.logs.forEach(log => {
        const parts = log.match(/^(\d{1,2}:\d{2}:\d{2}\s[AP]M):\s(.*)$/); // Basic time parsing
        if (parts) {
            allLogs.push({ timestamp: parts[1], message: parts[2], type: 'task' });
        } else {
            allLogs.push({ message: log, type: 'task'}); // Fallback if no timestamp
        }
    });
  });
  project.errorLog.forEach(err => allLogs.push({message: `[Project Error] ${err}`, type: 'project'}));

  // Crude sort by timestamp if available, otherwise append
  // This is a simplified sort; a robust solution would parse Date objects.
  allLogs.sort((a, b) => {
    if (a.timestamp && b.timestamp) return a.timestamp.localeCompare(b.timestamp);
    if (a.timestamp) return -1;
    if (b.timestamp) return 1;
    return 0; // Keep original order if no timestamps
  });


  return (
    <aside className="w-96 bg-gray-900/50 border-l border-gray-700 p-3 flex flex-col shrink-0">
      <h3 className="text-base font-semibold text-on-surface mb-3 flex items-center">
        <TerminalSquare size={18} className="mr-2 text-secondary" />
        AI Activity Log
      </h3>
      <div className="flex-grow bg-background p-2 rounded-md overflow-y-auto text-xs font-mono space-y-1.5 border border-gray-700 shadow-inner">
        {allLogs.length === 0 && <p className="text-gray-500">No activity yet for this project.</p>}
        {allLogs.map((logEntry, index) => (
          <div key={index} className={`whitespace-pre-wrap break-words ${
            logEntry.message.toLowerCase().includes('error') ? 'text-red-400' : 
            logEntry.message.toLowerCase().includes('[ai]') ? 'text-blue-300' :
            logEntry.message.toLowerCase().startsWith('[system]') ? 'text-gray-500' :
            'text-gray-400'
          }`}>
            {logEntry.timestamp && <span className="text-gray-600 mr-1.5">{logEntry.timestamp}</span>}
            <span>{logEntry.message}</span>
          </div>
        ))}
        <div ref={logsEndRef} />
      </div>
      {currentTask && (project.status === 'developing' || project.status === 'planning') && (
        <div className="mt-3 pt-3 border-t border-gray-700">
            <p className="text-xs text-on-surface-muted mb-1">Current Focus:</p>
            <p className="text-sm font-medium text-yellow-300">{currentTask.description}</p>
        </div>
      )}
       {project.status === 'error' && project.errorLog.length > 0 && (
        <div className="mt-3 pt-3 border-t border-gray-700">
            <p className="text-xs text-red-300 mb-1 font-semibold">Project Error:</p>
            <p className="text-sm text-red-400">{project.errorLog[project.errorLog.length -1]}</p>
        </div>
      )}
    </aside>
  );
};
