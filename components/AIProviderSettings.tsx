import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>s, Check, AlertCircle, Zap, Globe, Brain, Bot, Server } from 'lucide-react';
import { AIModel, AIProvider, AIProviderConfig } from '../types';
import { tauriService } from '../services/tauriService';

interface AIProviderSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

const providerIcons: Record<AIProvider, React.ReactNode> = {
  gemini: <Brain className="w-5 h-5" />,
  openrouter: <Globe className="w-5 h-5" />,
  openai: <Bot className="w-5 h-5" />,
  anthropic: <Zap className="w-5 h-5" />,
  ollama: <Server className="w-5 h-5" />,
};

const providerNames: Record<AIProvider, string> = {
  gemini: 'Google Gemini',
  openrouter: 'OpenRouter',
  openai: 'OpenAI',
  anthropic: 'Anthrop<PERSON> Claude',
  ollama: 'Ollama (Local)',
};

const providerDescriptions: Record<AIProvider, string> = {
  gemini: 'Google\'s powerful multimodal AI model',
  openrouter: 'Access to multiple free and paid models',
  openai: 'GPT models from OpenAI',
  anthropic: 'Claude models from Anthropic',
  ollama: 'Run models locally on your machine',
};

export const AIProviderSettings: React.FC<AIProviderSettingsProps> = ({ isOpen, onClose }) => {
  const [providers, setProviders] = useState<AIProviderConfig[]>([]);
  const [models, setModels] = useState<AIModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedProvider, setSelectedProvider] = useState<AIProvider | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadProviderData();
    }
  }, [isOpen]);

  const loadProviderData = async () => {
    try {
      setLoading(true);
      const [providersData, modelsData] = await Promise.all([
        tauriService.getEnabledProviders(),
        tauriService.getAvailableModels(),
      ]);
      setProviders(providersData);
      setModels(modelsData);
    } catch (error) {
      console.error('Failed to load provider data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getModelsForProvider = (provider: AIProvider) => {
    return models.filter(model => model.provider === provider);
  };

  const getProviderStatus = (provider: AIProvider) => {
    const config = providers.find(p => p.provider === provider);
    if (!config) return { enabled: false, hasKey: false };
    
    const hasKey = provider === 'ollama' || !!config.api_key;
    return { enabled: config.enabled, hasKey };
  };

  const getFreeModels = () => {
    return models.filter(model => 
      model.cost_per_token === 0 || 
      model.provider === 'ollama' ||
      (model.provider === 'openrouter' && model.id.includes(':free'))
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <Settings className="w-6 h-6 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">AI Provider Settings</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            ✕
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
            </div>
          ) : (
            <div className="space-y-8">
              {/* Free Models Section */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <Zap className="w-5 h-5 text-green-400" />
                  Free Models Available
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {getFreeModels().map((model) => {
                    const status = getProviderStatus(model.provider);
                    return (
                      <div
                        key={model.id}
                        className={`p-4 rounded-lg border ${
                          status.enabled && status.hasKey
                            ? 'border-green-500/30 bg-green-500/10'
                            : 'border-gray-600 bg-gray-700/50'
                        }`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            {providerIcons[model.provider]}
                            <div>
                              <h4 className="font-medium text-white">{model.name}</h4>
                              <p className="text-sm text-gray-400">{providerNames[model.provider]}</p>
                            </div>
                          </div>
                          {status.enabled && status.hasKey && (
                            <Check className="w-5 h-5 text-green-400" />
                          )}
                        </div>
                        <div className="mt-3 text-sm text-gray-300">
                          <p>Context: {model.context_window?.toLocaleString() || 'N/A'} tokens</p>
                          <p>Max Output: {model.max_tokens?.toLocaleString() || 'N/A'} tokens</p>
                          {!status.hasKey && model.provider !== 'ollama' && (
                            <p className="text-yellow-400 mt-2 flex items-center gap-1">
                              <AlertCircle className="w-4 h-4" />
                              API key required
                            </p>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* All Providers Section */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">All Providers</h3>
                <div className="space-y-4">
                  {Object.entries(providerNames).map(([provider, name]) => {
                    const status = getProviderStatus(provider as AIProvider);
                    const providerModels = getModelsForProvider(provider as AIProvider);
                    
                    return (
                      <div
                        key={provider}
                        className="border border-gray-600 rounded-lg p-4 bg-gray-700/30"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            {providerIcons[provider as AIProvider]}
                            <div>
                              <h4 className="font-medium text-white">{name}</h4>
                              <p className="text-sm text-gray-400">
                                {providerDescriptions[provider as AIProvider]}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {status.enabled && status.hasKey ? (
                              <span className="px-2 py-1 bg-green-500/20 text-green-400 rounded text-sm">
                                Ready
                              </span>
                            ) : status.enabled && !status.hasKey ? (
                              <span className="px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded text-sm">
                                Needs API Key
                              </span>
                            ) : (
                              <span className="px-2 py-1 bg-gray-500/20 text-gray-400 rounded text-sm">
                                Disabled
                              </span>
                            )}
                          </div>
                        </div>
                        
                        <div className="text-sm text-gray-300">
                          <p>{providerModels.length} models available</p>
                          {provider !== 'ollama' && !status.hasKey && (
                            <p className="text-yellow-400 mt-1">
                              Set {provider.toUpperCase()}_API_KEY environment variable to enable
                            </p>
                          )}
                        </div>

                        {selectedProvider === provider && (
                          <div className="mt-4 space-y-2">
                            <h5 className="font-medium text-white">Available Models:</h5>
                            {providerModels.map((model) => (
                              <div key={model.id} className="text-sm text-gray-300 pl-4">
                                • {model.name} ({model.id})
                              </div>
                            ))}
                          </div>
                        )}

                        <button
                          onClick={() => setSelectedProvider(
                            selectedProvider === provider ? null : provider as AIProvider
                          )}
                          className="mt-3 text-blue-400 hover:text-blue-300 text-sm"
                        >
                          {selectedProvider === provider ? 'Hide' : 'Show'} Models
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Setup Instructions */}
              <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-3">Setup Instructions</h3>
                <div className="space-y-3 text-sm text-gray-300">
                  <div>
                    <h4 className="font-medium text-white">Environment Variables:</h4>
                    <ul className="list-disc list-inside space-y-1 mt-1">
                      <li><code className="bg-gray-700 px-1 rounded">GEMINI_API_KEY</code> - Google AI Studio API key</li>
                      <li><code className="bg-gray-700 px-1 rounded">OPENROUTER_API_KEY</code> - OpenRouter API key (free tier available)</li>
                      <li><code className="bg-gray-700 px-1 rounded">OPENAI_API_KEY</code> - OpenAI API key</li>
                      <li><code className="bg-gray-700 px-1 rounded">ANTHROPIC_API_KEY</code> - Anthropic API key</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-white">Recommended Free Options:</h4>
                    <ul className="list-disc list-inside space-y-1 mt-1">
                      <li><strong>OpenRouter:</strong> Free tier with Llama, Phi-3, and Gemma models</li>
                      <li><strong>Ollama:</strong> Run models locally (no API key needed)</li>
                      <li><strong>Gemini:</strong> Generous free tier from Google</li>
                    </ul>
                  </div>
                  <p className="text-yellow-400">
                    Restart the application after setting environment variables.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
