// This component is largely superseded by TaskListPane and AiLogPane in the new project-based workflow.
// It can be removed or repurposed if a generic, non-project-specific AI interaction panel is needed elsewhere.
// For the current autonomous IDE structure, its functionality is integrated into other components.

import React from 'react';
// import { PromptInput } from './PromptInput'; // If used, ensure it's contextually appropriate
// import { LoadingSpinner } from './LoadingSpinner';
// import { ErrorMessage } from './ErrorMessage';
import { Sparkles } from 'lucide-react';

interface AiPanelProps {
  // Props would need to be redefined based on new potential use case
  // For example:
  // onSubmitPrompt?: (prompt: string) => void;
  // isLoading?: boolean;
  // error?: string | null;
  // logs?: string[];
}

export const AiPanel: React.FC<AiPanelProps> = (props) => {
  return (
    <aside className="w-80 bg-surface border-l border-gray-700 flex flex-col shrink-0">
      <div className="p-3 border-b border-gray-700 flex items-center space-x-2">
        <Sparkles size={20} className="text-secondary" />
        <h2 className="text-base font-semibold text-on-surface">AI Assistant</h2>
      </div>
      
      <div className="flex-grow p-3 text-sm text-on-surface-muted">
        <p>This panel is a placeholder or can be repurposed.</p>
        <p>Project-specific AI interactions are now handled in TaskListPane and AiLogPane.</p>
        {/* Example: Display logs if provided */}
        {/* {props.logs && props.logs.map((log, index) => <div key={index}>{log}</div>)} */}
      </div>

      {/* Example: Generic prompt input if needed */}
      {/* {props.onSubmitPrompt && (
        <div className="border-t border-gray-700">
          <PromptInput onSubmit={props.onSubmitPrompt} isLoading={props.isLoading || false} />
        </div>
      )} */}
    </aside>
  );
};
