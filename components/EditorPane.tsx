import React, { useState, useEffect, useRef } from 'react';
import { AppFile } from '../types';
import { Copy, Check, FileCode, Edit3, Save } from 'lucide-react';

// Ensure Prism is available globally
declare const Prism: any;

interface EditorPaneProps {
  file: AppFile | null;
  onContentChange: (filePath: string, newContent: string) => void;
}

export const EditorPane: React.FC<EditorPaneProps> = ({ file, onContentChange }) => {
  const [copied, setCopied] = useState<boolean>(false);
  const [editableContent, setEditableContent] = useState<string>('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);
  const codeRef = useRef<HTMLElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (file) {
      setEditableContent(file.content);
      setHasUnsavedChanges(false); // Reset unsaved changes when file changes
      if (codeRef.current && Prism && Prism.highlightElement) {
        // Timeout to ensure content is updated in DOM before highlighting
        setTimeout(() => {
           if (codeRef.current) Prism.highlightElement(codeRef.current);
        }, 0);
      }
    } else {
      setEditableContent('');
      setHasUnsavedChanges(false);
    }
  }, [file]);

  const handleCopy = async () => {
    if (file && editableContent) {
      try {
        await navigator.clipboard.writeText(editableContent);
        setCopied(true);
      } catch (err) {
        console.error("Failed to copy code:", err);
        alert("Failed to copy code to clipboard.");
      }
    }
  };

  useEffect(() => {
    if (copied) {
      const timer = setTimeout(() => setCopied(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [copied]);

  const handleContentEdit = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditableContent(e.target.value);
    if (file && e.target.value !== file.content) {
      setHasUnsavedChanges(true);
    } else {
      setHasUnsavedChanges(false);
    }
  };

  const handleSaveContent = () => {
    if (file && hasUnsavedChanges) {
      onContentChange(file.name, editableContent);
      setHasUnsavedChanges(false);
      // Re-highlight after save if needed, though ideally state sync handles this
      if (codeRef.current && Prism && Prism.highlightElement) {
        setTimeout(() => {
           if (codeRef.current) Prism.highlightElement(codeRef.current);
        }, 0);
      }
    }
  };
  
  // Sync textarea height with highlighted code block (basic approach)
  useEffect(() => {
    if (textareaRef.current && codeRef.current) {
      // Simple sync, might need more robust solution for complex cases
      textareaRef.current.style.height = 'auto'; // Reset height to shrink if content is smaller
      textareaRef.current.style.height = `${codeRef.current.scrollHeight}px`;
    }
  }, [editableContent, file]);


  if (!file) {
    return (
      <main className="flex-grow bg-background p-6 flex flex-col items-center justify-center text-on-surface-muted">
        <FileCode size={48} className="mb-4 text-gray-600" />
        <p className="text-lg">No file selected or project is empty.</p>
        <p className="text-sm">Select a file from the explorer or let the AI generate files.</p>
      </main>
    );
  }

  const languageClass = `language-${file.language || 'text'}`;

  return (
    <main className="flex-grow bg-background flex flex-col overflow-hidden">
      <div className="flex justify-between items-center px-4 py-2 bg-surface border-b border-gray-700 shrink-0">
        <span className="text-sm font-medium text-on-surface truncate flex items-center" title={file.name}>
          <Edit3 size={14} className="mr-2 text-primary/70"/>
          {file.name} {hasUnsavedChanges && <span className="ml-2 text-xs text-yellow-400">(modified)</span>}
        </span>
        <div>
          {hasUnsavedChanges && (
            <button
              onClick={handleSaveContent}
              className="flex items-center px-3 py-1 text-xs bg-green-600 text-white rounded-md hover:bg-green-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-surface focus:ring-green-500 mr-2"
              aria-label="Save changes"
            >
              <Save size={14} className="mr-1" />
              Save
            </button>
          )}
          <button
            onClick={handleCopy}
            disabled={!editableContent}
            className="flex items-center px-3 py-1 text-xs bg-primary text-white rounded-md hover:bg-primary-hover transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-surface focus:ring-primary disabled:opacity-50"
            aria-label="Copy code to clipboard"
          >
            {copied ? <Check size={14} className="mr-1" /> : <Copy size={14} className="mr-1" />}
            {copied ? 'Copied!' : 'Copy Code'}
          </button>
        </div>
      </div>
      <div className="flex-grow relative overflow-auto custom-scrollbar bg-gray-800"> {/* Okaidia background is dark, use a slightly lighter one for editor background */}
        {/* Editable Textarea - visually overlaps the pre/code block */}
        <textarea
          ref={textareaRef}
          value={editableContent}
          onChange={handleContentEdit}
          className={`absolute inset-0 w-full h-full p-4 font-mono text-transparent bg-transparent caret-white resize-none border-none outline-none overflow-auto ${languageClass} text-sm leading-tight custom-scrollbar line-numbers`}
          spellCheck="false"
          wrap="off" /* Better for code editing, rely on horizontal scroll */
        />
        {/* Displayed Code for Syntax Highlighting */}
        <pre className={`h-full p-4 ${languageClass} !bg-transparent custom-scrollbar line-numbers`} aria-hidden="true">
          <code ref={codeRef} className={`${languageClass}`}>
            {editableContent} 
          </code>
        </pre>
      </div>
       <style>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 10px;
          height: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #1f2937; /* surface */
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #374151; /* gray-700 */
          border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #4b5563; /* gray-600 */
        }
        /* Make textarea line numbers match Prism's */
        textarea.line-numbers {
          padding-left: 3.8em; /* Adjust if Prism's line number padding changes */
        }
      `}</style>
    </main>
  );
};