import React, { useState, useMemo } from 'react';
import { AppFile, FileSystemNode } from '../types';
import { FileText, Folder, ChevronDown, ChevronRight, Image as ImageIcon, Database, Settings, FileJson, FileCode2 } from 'lucide-react';

interface FileExplorerProps {
  files: AppFile[];
  selectedFilePath: string | null;
  onFileSelect: (filePath: string) => void;
}

const getFileIcon = (fileName: string, type: 'file' | 'folder', language?: string, isExpanded?: boolean) => {
  if (type === 'folder') {
    return isExpanded 
      ? <ChevronDown size={16} className="text-primary/80 mr-1 flex-shrink-0" /> 
      : <ChevronRight size={16} className="text-primary/80 mr-1 flex-shrink-0" />;
  }
  const ext = language || fileName.split('.').pop()?.toLowerCase();
  switch (ext) {
    case 'js': case 'javascript': return <FileCode2 size={16} className="text-yellow-400 mr-2 flex-shrink-0" />;
    case 'ts': case 'typescript': return <FileCode2 size={16} className="text-blue-400 mr-2 flex-shrink-0" />;
    case 'py': case 'python': return <FileCode2 size={16} className="text-green-400 mr-2 flex-shrink-0" />;
    case 'html': return <FileCode2 size={16} className="text-orange-500 mr-2 flex-shrink-0" />;
    case 'css': case 'scss': return <FileCode2 size={16} className="text-purple-400 mr-2 flex-shrink-0" />;
    case 'json': return <FileJson size={16} className="text-pink-400 mr-2 flex-shrink-0" />;
    case 'md': case 'markdown': return <FileText size={16} className="text-gray-400 mr-2 flex-shrink-0" />;
    case 'png': case 'jpg': case 'jpeg': case 'gif': case 'svg': return <ImageIcon size={16} className="text-teal-400 mr-2 flex-shrink-0" />;
    case 'db': case 'sql': return <Database size={16} className="text-indigo-400 mr-2 flex-shrink-0" />;
    case 'yaml': case 'yml': case 'toml': return <Settings size={16} className="text-red-400 mr-2 flex-shrink-0" />;
    case 'txt': return <FileText size={16} className="text-gray-500 mr-2 flex-shrink-0" />;
    default:
      return <FileText size={16} className="text-on-surface-muted mr-2 flex-shrink-0" />;
  }
};

const buildFileTree = (files: AppFile[]): FileSystemNode[] => {
  const root: FileSystemNode = { id: 'root', name: 'root', path: '', type: 'folder', children: [] };

  files.forEach(file => {
    const parts = file.name.split('/');
    let currentLevel = root;

    parts.forEach((part, index) => {
      const isLastPart = index === parts.length - 1;
      const existingPath = parts.slice(0, index + 1).join('/');
      
      let node = currentLevel.children?.find(child => child.name === part && (isLastPart ? child.type === 'file' : child.type === 'folder'));

      if (!node) {
        if (isLastPart) { // File node
          node = { 
            id: file.name, 
            name: part, 
            path: file.name, 
            type: 'file', 
            content: file.content, 
            language: file.language,
            lastModified: file.lastModified
          };
        } else { // Folder node
          node = { 
            id: existingPath, 
            name: part, 
            path: existingPath, 
            type: 'folder', 
            children: [] 
          };
        }
        currentLevel.children = currentLevel.children || [];
        currentLevel.children.push(node);
        currentLevel.children.sort((a, b) => { // Sort: folders first, then by name
          if (a.type === 'folder' && b.type === 'file') return -1;
          if (a.type === 'file' && b.type === 'folder') return 1;
          return a.name.localeCompare(b.name);
        });
      }
      currentLevel = node;
    });
  });
  return root.children || [];
};

interface FileTreeNodeProps {
  node: FileSystemNode;
  selectedFilePath: string | null;
  onFileSelect: (filePath: string) => void;
  onToggleFolder: (folderPath: string) => void;
  isExpanded: boolean;
  level: number;
}

const FileTreeNode: React.FC<FileTreeNodeProps> = ({ node, selectedFilePath, onFileSelect, onToggleFolder, isExpanded, level }) => {
  const handleSelect = () => {
    if (node.type === 'file') {
      onFileSelect(node.path);
    } else {
      onToggleFolder(node.path);
    }
  };

  return (
    <li key={node.id}>
      <button
        onClick={handleSelect}
        style={{ paddingLeft: `${level * 1 + 0.5}rem` }} // Indentation
        className={`w-full flex items-center py-1.5 text-xs rounded-md transition-colors duration-150
          ${selectedFilePath === node.path && node.type === 'file'
            ? 'bg-primary/20 text-primary-dark font-medium'
            : 'text-on-surface-muted hover:bg-gray-700/50 hover:text-on-surface'}
        `}
        aria-current={selectedFilePath === node.path && node.type === 'file' ? "page" : undefined}
        title={node.path}
      >
        {node.type === 'folder' 
          ? getFileIcon(node.name, 'folder', undefined, isExpanded) 
          : getFileIcon(node.name, 'file', node.language)}
        <span className="truncate">{node.name}</span>
      </button>
      {node.type === 'folder' && isExpanded && node.children && node.children.length > 0 && (
        <ul className="pl-0"> {/* No extra padding for sub-list, handled by node level */}
          {node.children.map(childNode => (
            <FileTreeNode
              key={childNode.id}
              node={childNode}
              selectedFilePath={selectedFilePath}
              onFileSelect={onFileSelect}
              onToggleFolder={onToggleFolder}
              isExpanded={isExpanded} // Pass parent's expanded state for now, will be specific to child later
              level={level + 1}
            />
          ))}
        </ul>
      )}
    </li>
  );
};


export const FileExplorer: React.FC<FileExplorerProps> = ({ files, selectedFilePath, onFileSelect }) => {
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});

  const fileTree = useMemo(() => buildFileTree(files), [files]);

  const handleToggleFolder = (folderPath: string) => {
    setExpandedFolders(prev => ({ ...prev, [folderPath]: !prev[folderPath] }));
  };
  
  const renderTreeNodes = (nodes: FileSystemNode[], level: number): JSX.Element[] => {
    return nodes.map(node => (
      <li key={node.id} className="list-none">
        <button
          onClick={() => node.type === 'file' ? onFileSelect(node.path) : handleToggleFolder(node.path)}
          style={{ paddingLeft: `${level * 0.75 + 0.5}rem` }} // Indentation: 0.75rem per level
          className={`w-full flex items-center py-1.5 text-xs rounded-md transition-colors duration-150 group
            ${selectedFilePath === node.path && node.type === 'file'
              ? 'bg-primary/20 text-primary-dark font-medium'
              : 'text-on-surface-muted hover:bg-gray-700/50 hover:text-on-surface'}
          `}
          aria-current={selectedFilePath === node.path && node.type === 'file' ? "page" : undefined}
          title={node.path}
        >
          {node.type === 'folder' 
            ? getFileIcon(node.name, 'folder', undefined, expandedFolders[node.path]) 
            : getFileIcon(node.name, 'file', node.language)}
          <span className="truncate">{node.name}</span>
        </button>
        {node.type === 'folder' && expandedFolders[node.path] && node.children && (
          <ul className="pl-0"> {/* No extra padding for sub-list, handled by node level */}
            {renderTreeNodes(node.children, level + 1)}
          </ul>
        )}
      </li>
    ));
  };


  return (
    <aside className="w-72 bg-surface border-r border-gray-700 p-3 flex flex-col shrink-0 overflow-y-auto">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-base font-semibold text-on-surface flex items-center">
         <Folder size={18} className="mr-2 text-primary/80" />
          Project Files
        </h3>
        <span className="text-xs px-2 py-0.5 bg-gray-700 text-on-surface-muted rounded-full">
          {files.length}
        </span>
      </div>
      
      {files.length === 0 ? (
        <p className="text-sm text-on-surface-muted py-4 text-center">No files yet. AI will generate them.</p>
      ) : (
        <ul className="space-y-0.5">
          {renderTreeNodes(fileTree, 0)}
        </ul>
      )}
    </aside>
  );
};