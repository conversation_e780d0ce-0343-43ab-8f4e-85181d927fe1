
import React from 'react';

interface LogoProps extends React.SVGProps<SVGSVGElement> {}

export const Logo: React.FC<LogoProps> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M14 2l4 4L6 18H2v-4L14 2z"></path>
    <path d="M3 21h18"></path>
    <path d="m15 6 6 6"></path>
  </svg>
);
    