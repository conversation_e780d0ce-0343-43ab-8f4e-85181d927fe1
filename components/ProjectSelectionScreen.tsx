import React from 'react';
import { AIProject } from '../types';
import { Folder, PlusCircle, Settings } from 'lucide-react';

interface ProjectSelectionScreenProps {
  projects: AIProject[];
  onSelectProject: (projectId: string) => void;
  onCreateNew: () => void;
  onOpenSettings?: () => void;
}

export const ProjectSelectionScreen: React.FC<ProjectSelectionScreenProps> = ({ projects, onSelectProject, onCreateNew, onOpenSettings }) => {
  return (
    <div className="p-6 sm:p-8 md:p-12 h-full flex flex-col items-center justify-center bg-gray-800/30">
      <div className="w-full max-w-2xl">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold text-on-surface">My AI Projects</h2>
          {onOpenSettings && (
            <button
              onClick={onOpenSettings}
              className="flex items-center gap-2 px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
              title="AI Provider Settings"
            >
              <Settings className="w-5 h-5" />
              AI Settings
            </button>
          )}
        </div>

        <button
          onClick={onCreateNew}
          className="w-full flex items-center justify-center p-4 mb-8 text-lg bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background focus:ring-primary"
        >
          <PlusCircle size={24} className="mr-3" />
          Create New Project
        </button>

        {projects.length === 0 ? (
          <p className="text-center text-on-surface-muted">No projects yet. Click above to create your first AI-powered project!</p>
        ) : (
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-on-surface mb-3">Existing Projects:</h3>
            {projects.sort((a,b) => new Date(b.lastUpdatedDate).getTime() - new Date(a.lastUpdatedDate).getTime()).map(project => (
              <div
                key={project.id}
                className="bg-surface p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer border border-gray-700 hover:border-primary/50"
                onClick={() => onSelectProject(project.id)}
                role="button"
                tabIndex={0}
                onKeyPress={(e) => e.key === 'Enter' && onSelectProject(project.id)}
              >
                <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-primary flex items-center">
                        <Folder size={20} className="mr-2 text-primary/80" />
                        {project.name}
                    </h3>
                    <span className={`px-2 py-0.5 text-xs rounded-full ${
                        project.status === 'completed' ? 'bg-green-500/20 text-green-300' :
                        project.status === 'review-pending' ? 'bg-yellow-500/20 text-yellow-300' :
                        project.status === 'error' ? 'bg-red-500/20 text-red-300' :
                        'bg-gray-600 text-gray-300'
                    }`}>
                        {project.status.replace('-', ' ')}
                    </span>
                </div>
                <p className="text-sm text-on-surface-muted mt-1 truncate" title={project.goal}>Goal: {project.goal}</p>
                <div className="text-xs text-gray-500 mt-2 flex justify-between">
                    <span>Tasks: {project.tasks.length}</span>
                    <span>Files: {project.files.length}</span>
                    <span>Progress: {project.overallProgress}%</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">Last updated: {new Date(project.lastUpdatedDate).toLocaleDateString()}</p>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
