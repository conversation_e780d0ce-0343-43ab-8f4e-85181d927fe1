import React, { useState } from 'react';
import { Loader2 } from 'lucide-react';

interface ProjectCreationScreenProps {
  onCreateProject: (name: string, goal: string) => Promise<void>;
  onCancel: () => void;
  isLoading: boolean;
  error: string | null;
}

export const ProjectCreationScreen: React.FC<ProjectCreationScreenProps> = ({ onCreateProject, onCancel, isLoading, error }) => {
  const [name, setName] = useState('');
  const [goal, setGoal] = useState('');
  const [formError, setFormError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);
    if (!name.trim()) {
      setFormError("Project name cannot be empty.");
      return;
    }
    if (!goal.trim()) {
      setFormError("Project goal cannot be empty.");
      return;
    }
    await onCreateProject(name, goal);
    // Parent will handle navigation on success
  };

  return (
    <div className="p-6 sm:p-8 md:p-12 h-full flex items-center justify-center bg-gray-800/30">
      <form onSubmit={handleSubmit} className="w-full max-w-lg bg-surface p-8 rounded-xl shadow-2xl border border-gray-700">
        <h2 className="text-2xl font-bold text-on-surface mb-6 text-center">Create New AI Project</h2>
        
        {error && <p className="mb-4 p-3 bg-red-500/20 text-red-200 text-sm rounded-md">{error}</p>}
        {formError && <p className="mb-4 p-3 bg-yellow-500/20 text-yellow-200 text-sm rounded-md">{formError}</p>}

        <div className="mb-6">
          <label htmlFor="projectName" className="block text-sm font-medium text-on-surface-muted mb-1">Project Name</label>
          <input
            type="text"
            id="projectName"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full p-3 bg-background border border-gray-600 rounded-md focus:ring-2 focus:ring-primary focus:border-primary transition-colors text-on-surface placeholder-gray-500"
            placeholder="e.g., My Awesome App"
            disabled={isLoading}
          />
        </div>

        <div className="mb-8">
          <label htmlFor="projectGoal" className="block text-sm font-medium text-on-surface-muted mb-1">Project Goal / Main Prompt</label>
          <textarea
            id="projectGoal"
            value={goal}
            onChange={(e) => setGoal(e.target.value)}
            rows={5}
            className="w-full p-3 bg-background border border-gray-600 rounded-md focus:ring-2 focus:ring-primary focus:border-primary transition-colors text-on-surface placeholder-gray-500 resize-none"
            placeholder="Describe what you want the AI to build. e.g., A Python Flask backend (using SQLite) for a blog with CRUD operations for posts and user authentication."
            disabled={isLoading}
          />
          <p className="text-xs text-gray-500 mt-1">Be specific. The AI will use this to plan development tasks (defaults to Python/Flask/SQLite for backends).</p>
        </div>

        <div className="flex items-center justify-between gap-4">
          <button
            type="button"
            onClick={onCancel}
            disabled={isLoading}
            className="px-6 py-3 text-sm bg-gray-600 hover:bg-gray-500 text-on-surface rounded-md transition-colors duration-200 disabled:opacity-50 w-1/2"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isLoading || !name.trim() || !goal.trim()}
            className="px-6 py-3 text-sm bg-primary hover:bg-primary-hover text-white rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-surface focus:ring-primary disabled:opacity-50 w-1/2 flex items-center justify-center"
          >
            {isLoading ? <Loader2 size={20} className="animate-spin mr-2" /> : null}
            {isLoading ? 'Creating...' : 'Create & Plan Tasks'}
          </button>
        </div>
      </form>
    </div>
  );
};