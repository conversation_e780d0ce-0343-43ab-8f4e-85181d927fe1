import React, { useState, useEffect } from 'react';
import { AIProject, AppFile, AITask } from '../types';
import { FileExplorer } from './FileExplorer';
import { EditorPane } from './EditorPane';
import { TaskListPane } from './TaskListPane';
import { AiLogPane } from './AiLogPane';
import { AlertTriangle, CheckCircle, Play, Pause, RotateCcw, UserCheck, ShieldQuestion, MessageSquarePlus } from 'lucide-react';

interface ProjectWorkspaceProps {
  project: AIProject;
  onStartAIDevelopment: () => Promise<void>;
  onUpdateProject: (updatedProject: AIProject) => void; 
  onConfirmReview: (confirm: boolean, feedback?: string) => void;
  onBackToProjects: () => void;
  onUpdateFileContent: (filePath: string, newContent: string) => void; // For editor changes
  onStartRefinement: (initialPrompt: string) => Promise<void>; // For starting refinement mode
}

export const ProjectWorkspace: React.FC<ProjectWorkspaceProps> = ({ 
  project, 
  onStartAIDevelopment,
  onUpdateProject,
  onConfirmReview,
  onBackToProjects,
  onUpdateFileContent,
  onStartRefinement
}) => {
  const [selectedFilePath, setSelectedFilePath] = useState<string | null>(null);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [reviewFeedback, setReviewFeedback] = useState('');
  const [showRefinementInput, setShowRefinementInput] = useState(false);
  const [refinementPrompt, setRefinementPrompt] = useState('');


  useEffect(() => {
    if (project.files.length > 0 && !selectedFilePath) {
      setSelectedFilePath(project.files[0].name);
    } else if (project.files.length > 0 && selectedFilePath && !project.files.find(f => f.name === selectedFilePath)) {
      setSelectedFilePath(project.files[0].name);
    } else if (project.files.length === 0) {
      setSelectedFilePath(null);
    }
  }, [project.files, selectedFilePath]);

  const selectedFile = project.files.find(f => f.name === selectedFilePath) || null;

  const handleFileSelect = (filePath: string) => {
    setSelectedFilePath(filePath);
  };

  const handleConfirmReviewAction = () => {
    onConfirmReview(true);
    setShowReviewModal(false);
  };

  const handleRejectReviewAction = () => {
    onConfirmReview(false, reviewFeedback);
    setShowReviewModal(false);
    setReviewFeedback('');
  };

  const handleInitiateRefinement = () => {
    if(refinementPrompt.trim()){
      onStartRefinement(refinementPrompt.trim());
      setRefinementPrompt('');
      setShowRefinementInput(false);
    }
  }

  const developmentInProgress = project.status === 'developing' || project.status === 'planning' || project.status === 'refining';
  const canStartDevelopment = project.status === 'ready-to-develop' || project.status === 'paused' || (project.status === 'error' && project.tasks.some(t => t.status !== 'completed'));
  const canRefineProject = project.status === 'completed' || project.status === 'error' || project.status === 'review-pending';


  const getProjectStatusIcon = () => {
    switch(project.status) {
        case 'developing': return <Play size={18} className="text-yellow-400 animate-pulse" />;
        case 'planning': return <RotateCcw size={18} className="text-blue-400 animate-spin" />;
        case 'refining': return <MessageSquarePlus size={18} className="text-teal-400 animate-pulse" />;
        case 'completed': return <CheckCircle size={18} className="text-green-400" />;
        case 'error': return <AlertTriangle size={18} className="text-red-400" />;
        case 'review-pending': return <UserCheck size={18} className="text-purple-400" />;
        case 'paused': return <Pause size={18} className="text-gray-400" />;
        default: return <ShieldQuestion size={18} className="text-gray-500" />;
    }
  }


  return (
    <div className="flex flex-col h-full">
      {/* Project Header Info */}
      <div className="p-3 bg-surface border-b border-gray-700 flex justify-between items-center shrink-0">
        <div>
          <h2 className="text-lg font-semibold text-on-surface" title={project.goal}>
            {project.name}
          </h2>
          <div className="flex items-center space-x-2 text-xs text-on-surface-muted">
            {getProjectStatusIcon()}
            <span>Status: {project.status.replace('-', ' ')}</span>
            <span>Progress: {project.overallProgress}%</span>
            <span>({project.tasks.filter(t=>t.status === 'completed').length}/{project.tasks.length} tasks)</span>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {canRefineProject && !showRefinementInput && (
             <button 
              onClick={() => setShowRefinementInput(true)}
              disabled={developmentInProgress}
              className="px-3 py-1.5 text-xs bg-teal-600 hover:bg-teal-500 text-white rounded-md transition-colors disabled:opacity-50"
            >
              Refine Project
            </button>
          )}
          {project.status === 'review-pending' && (
            <button 
              onClick={() => setShowReviewModal(true)}
              className="px-3 py-1.5 text-xs bg-purple-600 hover:bg-purple-500 text-white rounded-md transition-colors"
            >
              Review Project
            </button>
          )}
           {canStartDevelopment && (
            <button 
              onClick={onStartAIDevelopment}
              disabled={developmentInProgress}
              className="px-3 py-1.5 text-xs bg-primary hover:bg-primary-hover text-white rounded-md transition-colors disabled:opacity-50"
            >
              {developmentInProgress && project.status !== 'refining' ? 'AI Working...' : project.status === 'error' ? 'Retry Development' : 'Start AI Development'}
            </button>
          )}
        </div>
      </div>
      
      {showRefinementInput && (
        <div className="p-3 bg-surface border-b border-gray-700">
            <textarea 
                value={refinementPrompt}
                onChange={(e) => setRefinementPrompt(e.target.value)}
                placeholder="Describe the changes or new features you want the AI to implement..."
                rows={3}
                className="w-full p-2 bg-background border border-gray-600 rounded-md focus:ring-1 focus:ring-primary text-sm resize-none"
            />
            <div className="flex justify-end mt-2 space-x-2">
                <button onClick={() => setShowRefinementInput(false)} className="px-3 py-1 text-xs bg-gray-600 hover:bg-gray-500 rounded-md">Cancel</button>
                <button 
                    onClick={handleInitiateRefinement} 
                    disabled={!refinementPrompt.trim() || developmentInProgress}
                    className="px-3 py-1 text-xs bg-teal-500 hover:bg-teal-400 text-white rounded-md disabled:opacity-50"
                >
                    {project.status === 'refining' ? 'AI Refining...' : 'Send to AI'}
                </button>
            </div>
        </div>
      )}


      {/* Main Workspace Layout */}
      <div className="flex flex-grow overflow-hidden">
        <TaskListPane tasks={project.tasks} currentTaskIndex={project.currentTaskIndex} projectStatus={project.status} />
        <FileExplorer files={project.files} selectedFilePath={selectedFilePath} onFileSelect={handleFileSelect} />
        <EditorPane file={selectedFile} onContentChange={onUpdateFileContent} />
        <AiLogPane project={project} />
      </div>

      {/* Review Modal */}
      {showReviewModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-surface p-6 rounded-lg shadow-xl w-full max-w-md">
            <h3 className="text-xl font-semibold mb-4 text-on-surface">Project Review</h3>
            <p className="text-sm text-on-surface-muted mb-2">The AI has completed all tasks for the project "{project.name}". Please review the generated files.</p>
            <p className="text-sm text-on-surface-muted mb-4">Do you confirm that the project meets your requirements?</p>
            
            <div className="mb-4">
                <label htmlFor="reviewFeedback" className="block text-xs font-medium text-on-surface-muted mb-1">Feedback (optional, if rejecting):</label>
                <textarea
                    id="reviewFeedback"
                    value={reviewFeedback}
                    onChange={(e) => setReviewFeedback(e.target.value)}
                    rows={3}
                    className="w-full p-2 bg-background border border-gray-600 rounded-md focus:ring-1 focus:ring-primary text-sm"
                    placeholder="e.g., The login logic is missing error handling."
                />
            </div>

            <div className="flex justify-end space-x-3">
              <button 
                onClick={handleRejectReviewAction}
                className="px-4 py-2 text-sm bg-red-600 hover:bg-red-500 text-white rounded-md transition-colors"
              >
                Reject / Request Revisions
              </button>
              <button 
                onClick={handleConfirmReviewAction}
                className="px-4 py-2 text-sm bg-green-600 hover:bg-green-500 text-white rounded-md transition-colors"
              >
                Confirm & Complete Project
              </button>
            </div>
            <button onClick={() => setShowReviewModal(false)} className="absolute top-3 right-3 text-gray-400 hover:text-white">&times;</button>
          </div>
        </div>
      )}

    </div>
  );
};