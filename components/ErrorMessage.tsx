
import React from 'react';
import { AlertTriangle } from 'lucide-react';

interface ErrorMessageProps {
  message: string;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({ message }) => {
  if (!message) return null;

  return (
    <div className="w-full p-4 bg-red-500/20 border border-red-600 text-red-200 rounded-lg flex items-start space-x-3 shadow-lg">
      <AlertTriangle size={24} className="text-error flex-shrink-0 mt-0.5" />
      <div>
        <h3 className="font-semibold text-red-100">Error</h3>
        <p className="text-sm">{message}</p>
      </div>
    </div>
  );
};
    