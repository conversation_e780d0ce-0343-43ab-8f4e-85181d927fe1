import React from 'react';
import { AITask, AITaskStatus, AIProjectStatus } from '../types';
import { <PERSON><PERSON><PERSON>cle, AlertCircle, Loader2, Circle, Edit3, AlertTriangle, ShieldQuestion } from 'lucide-react';

interface TaskListPaneProps {
  tasks: AITask[];
  currentTaskIndex: number;
  projectStatus: AIProjectStatus;
}

const getStatusIcon = (status: AITaskStatus, isCurrent: boolean) => {
  if (status === 'in-progress' || (isCurrent && status !== 'completed' && status !== 'error')) {
    return <Loader2 size={16} className="animate-spin text-yellow-400 mr-2 flex-shrink-0" />;
  }
  switch (status) {
    case 'completed':
    case 'awaiting-review': // Task itself is done, project awaits review
      return <CheckCircle size={16} className="text-green-400 mr-2 flex-shrink-0" />;
    case 'error':
      return <AlertCircle size={16} className="text-red-500 mr-2 flex-shrink-0" />;
    case 'pending':
      return <Circle size={16} className="text-gray-500 mr-2 flex-shrink-0" />;
    default:
      return <ShieldQuestion size={16} className="text-gray-600 mr-2 flex-shrink-0" />;
  }
};

export const TaskListPane: React.FC<TaskListPaneProps> = ({ tasks, currentTaskIndex, projectStatus }) => {
  return (
    <aside className="w-72 bg-surface border-r border-gray-700 p-3 flex flex-col shrink-0 overflow-y-auto">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-base font-semibold text-on-surface flex items-center">
          <Edit3 size={18} className="mr-2 text-primary/80" />
          Development Tasks
        </h3>
        <span className="text-xs px-2 py-0.5 bg-gray-700 text-on-surface-muted rounded-full">
          {tasks.filter(t => t.status === 'completed').length} / {tasks.length}
        </span>
      </div>

      {tasks.length === 0 && projectStatus === 'ready-to-develop' && (
         <div className="text-center text-sm text-on-surface-muted py-4">
          <Loader2 size={24} className="animate-spin mx-auto mb-2 text-primary" />
          AI is planning tasks...
        </div>
      )}

      {tasks.length === 0 && projectStatus !== 'planning' && projectStatus !== 'ready-to-develop' &&(
        <p className="text-sm text-on-surface-muted py-4">No tasks planned yet. Start AI development to plan and execute tasks.</p>
      )}

      {tasks.length > 0 && (
        <ul className="space-y-1.5">
          {tasks.map((task, index) => {
            const isCurrent = index === currentTaskIndex && (projectStatus === 'developing' || projectStatus === 'planning');
            return (
              <li 
                key={task.id} 
                className={`p-2.5 rounded-md text-xs transition-all duration-150 
                  ${isCurrent ? 'bg-primary/20 ring-1 ring-primary/50 shadow-md' : 'bg-gray-700/30 hover:bg-gray-600/50'}
                  ${task.status === 'error' ? 'border-l-2 border-red-500' : ''}
                  ${task.status === 'completed' ? 'opacity-70' : ''}
                `}
                title={task.description}
              >
                <div className="flex items-start">
                  {getStatusIcon(task.status, isCurrent)}
                  <span className={`flex-grow truncate ${
                    isCurrent ? 'font-medium text-primary-dark' : 'text-on-surface-muted'
                  } ${task.status === 'completed' ? 'line-through text-gray-500' : ''}`}>
                    {task.description}
                  </span>
                </div>
                {task.status === 'error' && task.logs.find(log => log.includes('[AI] Error:')) && (
                   <p className="mt-1 pl-6 text-red-400 text-xxs truncate">
                     {task.logs.find(log => log.includes('[AI] Error:'))?.split('[AI] Error:')[1].trim()}
                   </p>
                )}
                {isCurrent && task.logs.length > 0 && (
                    <p className="mt-1 pl-6 text-blue-400 text-xxs truncate">
                        {task.logs[task.logs.length-1].split(': ').slice(2).join(': ')}
                    </p>
                )}

              </li>
            );
          })}
        </ul>
      )}
      {projectStatus === 'error' && tasks.every(t => t.status === 'completed' || t.status === 'error') && (
        <div className="mt-4 p-3 bg-red-500/10 border border-red-500/30 rounded-md text-center">
            <AlertTriangle size={24} className="mx-auto mb-2 text-red-400" />
            <p className="text-sm font-semibold text-red-300">Development Halted</p>
            <p className="text-xs text-red-400">An error occurred. Check logs for details. You may need to revise the goal or retry.</p>
        </div>
      )}
    </aside>
  );
};
