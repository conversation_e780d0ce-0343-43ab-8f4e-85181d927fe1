import React, { useState } from 'react';
import { SendHorizonal } from 'lucide-react';

interface PromptInputProps {
  onSubmit: (prompt: string) => void;
  isLoading: boolean;
  initialPrompt?: string;
  onPromptChange?: (prompt: string) => void;
  placeholderText?: string;
  submitButtonText?: string;
  descriptionText?: string;
}

export const PromptInput: React.FC<PromptInputProps> = ({ 
  onSubmit, 
  isLoading, 
  initialPrompt = '', 
  onPromptChange,
  placeholderText = "e.g., Create a Python Flask app...",
  submitButtonText = "Run AI Task",
  descriptionText = "Describe the project, files, or changes you want the AI to make." 
}) => {
  const [currentPrompt, setCurrentPrompt] = useState<string>(initialPrompt);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (currentPrompt.trim()) {
      onSubmit(currentPrompt);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCurrentPrompt(e.target.value);
    if (onPromptChange) {
      onPromptChange(e.target.value);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col h-full">
      <textarea
        value={currentPrompt}
        onChange={handleChange}
        placeholder={placeholderText}
        className="flex-grow w-full p-3 bg-background border-t border-b border-gray-700 focus:ring-1 focus:ring-primary focus:border-primary resize-none text-on-surface placeholder-gray-500 transition-colors duration-200 text-sm"
        disabled={isLoading}
        rows={10}
        aria-label="AI Development Prompt"
      />
      {descriptionText && (
        <p className="p-3 text-xs text-on-surface-muted bg-surface border-b border-gray-700">
          {descriptionText}
        </p>
      )}
      <button
        type="submit"
        disabled={isLoading || !currentPrompt.trim()}
        className="w-full flex items-center justify-center p-3 bg-primary text-white hover:bg-primary-hover disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-surface focus:ring-primary"
        aria-label="Submit to AI"
      >
        {isLoading ? (
          <svg className="animate-spin h-5 w-5 text-white mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        ) : (
          <SendHorizonal size={18} className="mr-2" />
        )}
        {isLoading ? 'Processing...' : submitButtonText}
      </button>
    </form>
  );
};
