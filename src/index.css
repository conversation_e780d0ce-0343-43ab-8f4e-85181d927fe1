@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for the AI IDE */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-background text-on-surface;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-surface;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-600 rounded;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500;
  }
}

@layer components {
  /* Button styles */
  .btn-primary {
    @apply bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg font-medium transition-colors;
  }
  
  .btn-secondary {
    @apply bg-surface hover:bg-gray-700 text-on-surface px-4 py-2 rounded-lg font-medium transition-colors border border-gray-600;
  }
  
  .btn-danger {
    @apply bg-error hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors;
  }
  
  /* Input styles */
  .input-primary {
    @apply bg-surface border border-gray-600 text-on-surface px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
  }
  
  /* Card styles */
  .card {
    @apply bg-surface border border-gray-700 rounded-lg p-4;
  }
  
  .card-hover {
    @apply card hover:border-gray-600 transition-colors cursor-pointer;
  }
  
  /* Status indicators */
  .status-idle {
    @apply text-gray-400;
  }
  
  .status-planning {
    @apply text-yellow-400;
  }
  
  .status-developing {
    @apply text-blue-400;
  }
  
  .status-review-pending {
    @apply text-orange-400;
  }
  
  .status-completed {
    @apply text-green-400;
  }
  
  .status-error {
    @apply text-red-400;
  }
  
  .status-refining {
    @apply text-purple-400;
  }
}

@layer utilities {
  /* Custom utilities */
  .text-gradient {
    background: linear-gradient(135deg, #4f46e5, #06b6d4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(26, 26, 46, 0.8);
  }
}
